import { useContext } from 'react'
import { cn } from '@/lib/utils'
import { Icons } from '@/components/ui/icons'
import { Button } from '@/components/ui/button'
import { AuthContext } from '@/context/authContext'
import { validateGoogleAuth } from '@/api/auth'
import { useGoogleLogin } from '@react-oauth/google'
import { Navigate } from 'react-router-dom'
import { toast } from 'sonner'
import { useTranslation } from 'react-i18next'

interface UserAuthFormProps
  extends React.HTMLAttributes<HTMLDivElement> {}

export function UserAuthForm({
  className,
  ...props
}: UserAuthFormProps) {
  const { t } = useTranslation() 
  const { isLoggedIn, loading, login } = useContext(AuthContext)

  const googleLogin = useGoogleLogin({
    onSuccess: async (codeResponse) => {
      try {
        const authInfo = await validateGoogleAuth(codeResponse.code)
        login(authInfo)
      } catch (error) {
        toast.error(
          t('errorLoggingInWithGoogle') + // Usar la traducción
            error.response.data.message
        )
      }
    },
    flow: 'auth-code',
    hosted_domain: 'yoizen.com',
  })

  if (isLoggedIn) {
    return <Navigate to="/" />
  }

  return (
    <div className={cn('grid gap-6', className)} {...props}>
      <Button
        variant="custom" // Usar la nueva variante personalizada
        type="button"
        disabled={loading}
        onClick={googleLogin}
      >
        {loading ? (
          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <Icons.google className="mr-2 h-4 w-4" />
        )}{' '}
        {t('google')} {}
      </Button>
    </div>
  )
}
