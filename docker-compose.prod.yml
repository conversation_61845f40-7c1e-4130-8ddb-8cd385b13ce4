version: "3.8"
services:
  # Backend service configuration
  app:
    env_file:
      - .env.production
    environment:
      NODE_ENV: production
    build:
      context: ./api
      dockerfile: Dockerfile.prod
    depends_on:
      - mysql
    ports:
      - "${NODE_LOCAL_PORT}:${NODE_DOCKER_PORT}"
    restart: always

  # Database service configuration
  mysql:
    image: mysql/mysql-server:8.0.23
    environment:
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
    ports:
      - "${DB_LOCAL_PORT}:${DB_DOCKER_PORT}"
    volumes:
      - db:/var/lib/mysql
      - ./api/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: always


  # Frontend service configuration
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.prod
    environment:
      NODE_ENV: production
    ports:
      - "8080:8080"  # Map host port 8080 to container port 8080
    restart: always

volumes:
  db:


