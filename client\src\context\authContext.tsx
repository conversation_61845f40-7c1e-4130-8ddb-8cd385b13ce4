/* eslint-disable react-hooks/exhaustive-deps */
import { ReactNode, createContext, useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { validateToken } from '@/api/auth'
import AuthInfo from '@/types/authInfo'
import { useQueryClient } from '@tanstack/react-query'

interface AuthContextType {
  isLoggedIn: boolean
  login: (authInfo: AuthInfo) => void
  logout: () => void
  loading: boolean
  getUserInfo: () => AuthInfo
}

const defaultAuthContext: AuthContextType = {
  isLoggedIn: false,
  login: () => {},
  logout: () => {},
  loading: false,
  getUserInfo: () => {
    return { token: '', role: '', clientId: '' }
  },
}

export const AuthContext = createContext<AuthContextType>(
  defaultAuthContext
)

interface AuthProviderProps {
  children: ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({
  children,
}) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [loading, setLoading] = useState(true)
  const navigate = useNavigate()
  const location = useLocation()
  const queryClient = useQueryClient()

  useEffect(() => {
    const checkToken = async (token: string) => {
      try {
        const data = await validateToken(token)
        login(data)
      } catch (error) {
        console.error('Error validating token:', error)
        logout()
      }
    }

    const urlParams = new URLSearchParams(window.location.search)
    const authToken =
      urlParams.get('auth') || localStorage.getItem('yoizen_token')

    if (authToken) {
      checkToken(authToken)
    } else {
      setLoading(false)
    }
  }, [])
  const login = (authInfo: AuthInfo) => {
    const { token, client, name, email, role, clientId } = authInfo
	console.log("auth info: ", authInfo)
    localStorage.setItem('yoizen_token', token)
    localStorage.setItem('yoizen_role', role)
    if (client) {
      localStorage.setItem('yoizen_client', client)
    }
    
    if (clientId) {
      localStorage.setItem('yoizen_client_id', String(clientId))
    }

    if (name) {
      localStorage.setItem('yoizen_name', name)
    }

    if (email) {
      localStorage.setItem('yoizen_email', email)
    }

    setIsLoggedIn(true)
    setLoading(false)
    navigate(location.pathname)
  }

  const logout = () => {
    localStorage.clear()
    queryClient.clear()
    setIsLoggedIn(false)
    setLoading(false)
    navigate('/auth/signin')
  }
  const getUserInfo = () => {
    const token = localStorage.getItem('yoizen_token') ?? ''
    const client = localStorage.getItem('yoizen_client') ?? ''
    const name = localStorage.getItem('yoizen_name') ?? ''
    const email = localStorage.getItem('yoizen_email') ?? ''
    const role = localStorage.getItem('yoizen_role') ?? ''
    const clientId = localStorage.getItem('yoizen_client_id') ?? ''

    return { token, client, name, email, role, clientId }
  }

  return (
    <AuthContext.Provider
      value={{ isLoggedIn, login, logout, loading, getUserInfo }}
    >
      {children}
    </AuthContext.Provider>
  )
}
