# Meta Invoice Extractor Kubernetes Deployment

This directory contains Kubernetes configuration files for deploying the Meta Invoice Extractor service.

## Configuration Files

- `deployment.yaml`: Main deployment configuration with container settings and environment variables
- `azure-storage.yaml`: Storage class and PVC for Azure Blob Storage integration
- `configmap.yaml`: Configuration data including initialization scripts
- `ingress.yaml`: Ingress configuration for external access
- `create-secrets.sh`: Script to generate Kubernetes secrets from .env file

## Directory Structure in Azure Storage

The application uses the following directory structure for processing invoices:

- `/app/data/input`: Directory for incoming invoice PDF files
- `/app/data/output`: Directory for processed output files
- `/app/data/processed`: Directory for successfully processed files
- `/app/data/error`: Directory for files that failed processing

## Deployment Instructions

1. Update the Azure Storage parameters in `azure-storage.yaml` to match your Azure environment.

2. Create the storage resources:
   ```
   kubectl apply -f azure-storage.yaml
   ```

3. Generate the secrets from your .env file:
   ```
   ./create-secrets.sh
   ```

4. Review and apply the generated secret.yaml:
   ```
   kubectl apply -f secret.yaml
   ```

5. Apply the ConfigMap:
   ```
   kubectl apply -f configmap.yaml
   ```

6. Deploy the application:
   ```
   kubectl apply -f deployment.yaml
   ```

7. Configure the ingress (update the domain name first):
   ```
   kubectl apply -f ingress.yaml
   ```

## Monitoring and Troubleshooting

- Check deployment status:
  ```
  kubectl get deployments
  kubectl describe deployment meta-invoice-extractor
  ```

- View logs:
  ```
  kubectl logs -f deployment/meta-invoice-extractor
  ```

- Check the PVC status:
  ```
  kubectl get pvc
  kubectl describe pvc meta-invoice-pvc
  ```