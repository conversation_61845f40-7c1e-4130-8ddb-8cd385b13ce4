# API de la Consola Yoizen - Configuración del Entorno de Desarrollo

Este README proporciona instrucciones para configurar el entorno de desarrollo para el proyecto de la API de la Consola Yoizen.

## Prerrequisitos

- Docker y Docker Compose
- Node.js  >= 18
- pnpm

## Configuración

El proyecto utiliza Docker Compose para la sincronización de la configuración. Para producción, use `docker-compose.prod.yml`.

### Variables de Entorno

1. En la raíz del proyecto API (backend), cree dos archivos `.env`:

`.env.development`:

DB_USER=usuario_db
DB_PASSWORD=password_db
DB_NAME=yoizen_console
DB_HOST=mysql
DB_LOCAL_PORT=3306
DB_DOCKER_PORT=3306
NODE_LOCAL_PORT=3006
NODE_DOCKER_PORT=3006
VITE_LOCAL_PORT=8080
VITE_DOCKER_PORT=8080
NODEMON_FLAGS=
JWT_SECRET=RacingCampeon2024
GOOGLE_CLIENT_ID= --Obtener de Bitwarden
GOOGLE_CLIENT_SECRET= --Obtener de Bitwarden
GOOGLE_CLIENT_REDIRECT_URL=http://localhost:8080
QUEUE_CONNECTION_STRING= --Obtener de Bitwarden
QUEUE_NAME= --Obtener de Bitwarden


`.env.production`:

# Igual que .env.development, pero cambie las variables según sea necesario para producción.
# Actualice esta variable cuando tenga la URL y el puerto de despliegue:
GOOGLE_CLIENT_REDIRECT_URL=http://threestarsconsulting.com:8080


2. En el directorio del proyecto CLIENT (frontend), cree dos archivos `.env`:

`.env.development`:

VITE_API_URL=http://localhost:3006/api
VITE_PORT=8080


`.env.production`:

VITE_API_URL=http://threestarsconsulting.com:3006/api
# Actualice esta URL cuando se complete el despliegue del backend

### Configuración del archivo public/config.js

Para ejecutar la aplicación localmente sin Docker, es necesario configurar correctamente el archivo `client/public/config.js`. Este archivo contiene la configuración de variables de entorno que se utilizarán en tiempo de ejecución por la aplicación React/Vite.

El archivo debe contener lo siguiente:

```javascript
window._env_ = {
    API_URL: "http://localhost:3006/api",
    CLIENT_PORT: "8080",
    GOOGLE_CLIENT_ID: "tu-id-de-cliente-de-google"
};
```

Es importante que el valor de `GOOGLE_CLIENT_ID` coincida con el configurado en el archivo `.env` del backend (variable `GOOGLE_CLIENT_ID`) para que la autenticación funcione correctamente. Este ID debe obtenerse de Bitwarden según lo indicado anteriormente.

### Ejecución local sin Docker

Para ejecutar la aplicación localmente sin Docker, siga estos pasos:

1. Configure los archivos `.env` en la carpeta `api` según se indicó anteriormente.
2. Configure el archivo `client/public/config.js` con los valores correctos.
3. En la carpeta `api`, ejecute:
   ```
   npm install
   npm run dev
   ```
4. En la carpeta `client`, ejecute:
   ```
   npm install
   npm run dev
   ```

La aplicación estará disponible en http://localhost:8080 (o el puerto que haya configurado).


## Iniciando el Entorno de Desarrollo

Para iniciar el entorno de desarrollo, ejecute el siguiente comando en la raíz del proyecto:

pnpm run build-dev
    -- Si Arroja error, replicar el .env creado en "API"  en el root de la solucion (Ruta donde este el docker.compose)

Este comando:
- Iniciará el frontend (aplicación React) usando Vite
- Iniciará el backend (Node.js + Express) usando Nodemon
- Iniciará la base de datos MySQL usando Docker

Despues de iniciar el entorno, puede acceder a la aplicación en http://localhost:8080.
    Si falta seedear la base de datos, ejecute el siguiente comando:
     Para el entorno de DEV: `pnpm run seed` cd
    -- Si da error el pnpm run seed probar
        `
        docker-compose --env-file .env.development run app sh -c 'npx tsc'
        docker-compose --env-file .env.development run app sh -c 'npx sequelize-cli db:seed:all'        `

## Características de Desarrollo

- Vite y Nodemon proporcionan recarga en caliente, reflejando automáticamente los cambios de código en las aplicaciones.
- El contenedor MySQL incluye un script de inicialización en la carpeta `mysql` para crear la base de datos y el usuario en la primera ejecución.
- En modo de desarrollo, los modelos de Sequelize se sincronizan con las tablas de la base de datos usando `await sequelize.sync()`.

## Gestión de la Base de Datos

- Para producción, las tablas de la base de datos se crean usando migraciones.
- Una migración inicial está disponible en la carpeta `migrations`.

## Comandos Útiles

- `pnpm run seed`: Poblar la base de datos con información de los seeders (ver carpeta `database/seeders`)
- `pnpm run seed-production`: Igual que el anterior, pero usando el entorno de producción
- `pnpm run build-dev`: Iniciar todo con Docker Compose en modo de desarrollo
- `pnpm run build-prod`: Iniciar todo con Docker Compose en modo de producción
- `pnpm run migrate-prod`: Ejecutar migraciones (esencial para la migración inicial, migraciones posteriores según sea necesario)

## Autenticación

- Autenticación de Google: Agregar usuarios de Yoizen a los grupos respectivos:
    QA:
    - Grupo de administradores: '<EMAIL>'
    - Grupo de visualizadores: '<EMAIL>'
    Prod:
    - Grupo de administradores: '<EMAIL>'
    - Grupo de visualizadores: '<EMAIL>'
- Tokens de Usuario: Generar tokens usando el JWT_SECRET especificado en el archivo .env.

Nota: Hay un endpoint comentado para generar tokens (para ejemplos si es necesario).