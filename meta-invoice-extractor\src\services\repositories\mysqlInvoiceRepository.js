import mysql from 'mysql2/promise';

export default class MySQLInvoiceRepository {
  constructor(config) {
    this.config = config;
    console.log('MySQL repository initialized with config:', JSON.stringify({
      host: config.host,
      database: config.database,
      user: '***',
      password: '***'
    }));
    
    this.connectionPool = mysql.createPool(config);
  }

  async saveInvoice(invoiceId, data) {
    try {
      console.log(`[MySQL] Saving invoice ${invoiceId} to database`);
      
      // First save invoice summary data
      await this.saveInvoiceSummary(invoiceId, data.invoiceSummary);
      
      // Then save accounts and their items
      if (data.accounts && Array.isArray(data.accounts)) {
        await this.saveAccounts(invoiceId, data.accounts);
      }
      
      return Promise.resolve();
    } catch (error) {
      console.error('[MySQL] Error saving invoice:', error);
      throw error;
    }
  }

  async saveInvoiceSummary(invoiceId, summaryData) {
    const connection = await this.connectionPool.getConnection();
    try {
      const { invoiceDate, listPrice, partnerDiscount, subtotal, tax, totalDue } = summaryData;
      
      // Format date from 'DD-MMM-YYYY' to 'YYYY-MM-DD'
      const dateFormatted = this.formatDate(invoiceDate);
      
      const query = `
        INSERT INTO invoice_summaries (
          invoice_id, invoice_date, list_price, partner_discount, 
          subtotal, tax, total_due, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW()) 
        ON DUPLICATE KEY UPDATE 
          invoice_date = ?,
          list_price = ?,
          partner_discount = ?,
          subtotal = ?,
          tax = ?,
          total_due = ?,
          updated_at = NOW()
      `;
      
      await connection.execute(query, [
        invoiceId, dateFormatted, listPrice, partnerDiscount, 
        subtotal, tax, totalDue,
        // For update part
        dateFormatted, listPrice, partnerDiscount, 
        subtotal, tax, totalDue
      ]);
      
    } finally {
      connection.release();
    }
  }

  async saveAccounts(invoiceId, accounts) {
    const connection = await this.connectionPool.getConnection();
    try {
      await connection.beginTransaction();
      
      for (const account of accounts) {
        // Save account
        const accountId = await this.saveAccount(connection, invoiceId, account);
        
        // Save account items
        if (account.items && Array.isArray(account.items)) {
          await this.saveAccountItems(connection, invoiceId, accountId, account.items);
        }
      }
      
      await connection.commit();
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  }

  async saveAccount(connection, invoiceId, account) {
    const { name, accountNumber, totalUSD } = account;
    
    const query = `
      INSERT INTO invoice_accounts (
        invoice_id, account_name, account_number, total_usd, created_at
      ) VALUES (?, ?, ?, ?, NOW())
      ON DUPLICATE KEY UPDATE
        account_name = ?,
        total_usd = ?,
        updated_at = NOW()
    `;
    
    const [result] = await connection.execute(query, [
      invoiceId, name, accountNumber, totalUSD,
      // For update part
      name, totalUSD
    ]);
    
    // If it's an update, get existing ID
    if (result.insertId === 0) {
      const [rows] = await connection.execute(
        'SELECT id FROM invoice_accounts WHERE invoice_id = ? AND account_number = ?',
        [invoiceId, accountNumber]
      );
      return rows[0].id;
    }
    
    return result.insertId;
  }

  async saveAccountItems(connection, invoiceId, accountId, items) {
    // First delete existing items for this account to avoid duplicates
    await connection.execute(
      'DELETE FROM invoice_account_items WHERE account_id = ?',
      [accountId]
    );
    
    // Insert all new items
    for (const item of items) {
      const { region, type, priceType, conversations, total } = item;
      
      const query = `
        INSERT INTO invoice_account_items (
          account_id, invoice_id, region, item_type, price_type, 
          conversations, total, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
      `;
      
      await connection.execute(query, [
        accountId, invoiceId, region, type, priceType, 
        conversations, total
      ]);
    }
  }

  async getInvoice(invoiceId) {
    try {
      console.log(`[MySQL] Retrieving invoice ${invoiceId} from database`);
      
      const connection = await this.connectionPool.getConnection();
      try {
        // Get invoice summary
        const [summaryRows] = await connection.execute(
          'SELECT * FROM invoice_summaries WHERE invoice_id = ?',
          [invoiceId]
        );
        
        if (summaryRows.length === 0) {
          throw new Error(`Invoice ${invoiceId} not found`);
        }
        
        // Get accounts
        const [accountRows] = await connection.execute(
          'SELECT * FROM invoice_accounts WHERE invoice_id = ?',
          [invoiceId]
        );
        
        // Get account items
        const accounts = [];
        for (const account of accountRows) {
          const [itemRows] = await connection.execute(
            'SELECT * FROM invoice_account_items WHERE account_id = ?',
            [account.id]
          );
          
          const items = itemRows.map(item => ({
            region: item.region,
            type: item.item_type,
            priceType: item.price_type,
            conversations: item.conversations,
            total: item.total
          }));
          
          accounts.push({
            name: account.account_name,
            accountNumber: account.account_number,
            items,
            totalUSD: account.total_usd
          });
        }
        
        // Construct the invoice data
        const summary = summaryRows[0];
        return {
          invoiceSummary: {
            invoiceNumber: invoiceId,
            invoiceDate: this.formatDateToDisplay(summary.invoice_date),
            listPrice: summary.list_price,
            partnerDiscount: summary.partner_discount,
            subtotal: summary.subtotal,
            tax: summary.tax,
            totalDue: summary.total_due
          },
          accounts,
          invoiceTotal: summary.list_price,
          valid: true
        };
      } finally {
        connection.release();
      }
    } catch (error) {
      console.error('[MySQL] Error retrieving invoice:', error);
      throw error;
    }
  }
  
  // Helper method to format date from 'DD-MMM-YYYY' to 'YYYY-MM-DD'
  formatDate(dateStr) {
    if (!dateStr) return null;
    
    const parts = dateStr.split('-');
    if (parts.length !== 3) return dateStr;
    
    const day = parts[0];
    const monthMap = {
      'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04', 'May': '05', 'Jun': '06',
      'Jul': '07', 'Aug': '08', 'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
    };
    const month = monthMap[parts[1]] || '01';
    const year = parts[2];
    
    return `${year}-${month}-${day}`;
  }
  
  // Helper method to format date from DB 'YYYY-MM-DD' to 'DD-MMM-YYYY'
  formatDateToDisplay(dateStr) {
    if (!dateStr) return null;
    
    const date = new Date(dateStr);
    const day = date.getDate().toString().padStart(2, '0');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const month = monthNames[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day}-${month}-${year}`;
  }
}