import axios from 'axios'
import { API_URL } from './constants'
import AuthInfo from '../types/authInfo'

export const validateToken = async (
  token: string
): Promise<AuthInfo> => {
  try {
    const response = await axios.post(
      `${API_URL}/auth/validate_token`,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          authorization: `Bearer ${token}`,
        },
      }
    )
    return response.data
  } catch (error) {
    console.error('Error validating token', error)
    throw error
  }
}

export const validateGoogleAuth = async (
  code: string
): Promise<AuthInfo> => {
  try {
    const response = await axios.post(
      `${API_URL}/auth/validate_google_auth_code`,
      { googleAuthCode: code }
    )
    return response.data
  } catch (error) {
    console.error('Error validating google auth code', error)
    throw error
  }
}


