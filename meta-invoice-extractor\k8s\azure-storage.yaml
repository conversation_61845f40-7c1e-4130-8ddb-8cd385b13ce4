kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: azurefile
provisioner: kubernetes.io/azure-file
parameters:
  skuName: Standard_LRS
  location: westeurope  # Change to your Azure region
  storageAccount: metainvoicestorage  # Change to your storage account name
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: meta-invoice-pvc
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: azurefile
  resources:
    requests:
      storage: 5Gi  # Adjust size as needed