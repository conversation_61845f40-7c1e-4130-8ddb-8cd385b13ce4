import fs from 'fs';
import path from 'path';
import processInvoicePdf from './invoiceExtractor/index.js';
import directoryManager, { invoicesDir, outputDir, processedDir, errorDir } from './directoryManager.js';

/**
 * Initialize the invoice file watcher
 */
const initInvoiceWatcher = () => {
  // Ensure directories are created
  directoryManager.getDirectories();

  console.log(`Watching for changes in directory: ${invoicesDir}`);

  fs.watch(invoicesDir, (eventType, filename) => {
    if (filename && eventType === 'rename' && filename.toLowerCase().endsWith('.pdf')) {
      console.log(`New invoice file detected: ${filename}`);
      const filePath = path.join(invoicesDir, filename);
      
      // Add a delay to make sure the file is completely written
      setTimeout(() => {
        // Verify the file still exists before processing
        if (fs.existsSync(filePath)) {
          // Process the PDF file asynchronously
          processInvoicePdf(filename)
            .then(result => {
              if (result) {
                console.log(`Successfully processed invoice: ${filename}`);
              }
            })
            .catch(err => {
              console.error(`Failed to process invoice ${filename}:`, err);
            });
        } else {
          console.log(`File ${filename} no longer exists, skipping processing`);
        }
      }, 1000); // 1 second delay
    }
  });
};

export { initInvoiceWatcher, invoicesDir, outputDir, processedDir, errorDir };