#!/bin/bash

# Load environment variables from .env
if [ -f ../.env ]; then
  source ../.env
else
  echo "Error: .env file not found"
  exit 1
fi

# Create the secret
kubectl create secret generic meta-invoice-secrets \
  --from-literal=mysql-host="${MYSQL_HOST}" \
  --from-literal=mysql-user="${MYSQL_USER}" \
  --from-literal=mysql-password="${MYSQL_PASSWORD}" \
  --from-literal=mysql-database="${MYSQL_DATABASE}" \
  --from-literal=whatsapp-db-host="${WHATSAPP_DB_HOST}" \
  --from-literal=whatsapp-db-user="${WHATSAPP_DB_USER}" \
  --from-literal=whatsapp-db-password="${WHATSAPP_DB_PASSWORD}" \
  --from-literal=whatsapp-db-name="${WHATSAPP_DB_NAME}" \
  --from-literal=centralizer-endpoint="${CENTRALIZER_ENDPOINT}" \
  --from-literal=centralizer-auth-salt="${CENTRALIZER_AUTH_SALT}" \
  --dry-run=client -o yaml > secret.yaml

echo "Secret configuration generated in secret.yaml"
echo "Review the file and apply with: kubectl apply -f secret.yaml"