export default class InvoiceRepository {
  constructor(storageAdapter, outputDirName = 'output') {
    this.storageAdapter = storageAdapter;
    this.outputDirName = outputDirName; // Using the logical name expected by storageAdapter
  }

  async saveInvoice(invoiceId, data) {
    await this.storageAdapter.writeFile(`${this.outputDirName}/${invoiceId}.json`, data);
  }

  async getInvoice(invoiceId) {
    const buffer = await this.storageAdapter.readFile(`${this.outputDirName}/${invoiceId}.json`);
    return JSON.parse(buffer.toString());
  }
}