/**
 * Helper function to split string by multiple delimiters
 */
function splitByDelimiters(str, delimiters) {
  let parts = [str];
  
  for (const delimiter of delimiters) {
    let newParts = [];
    for (const part of parts) {
      const splitPart = part.split(delimiter);
      if (splitPart.length > 1) {
        newParts = [...newParts, ...splitPart];
      } else {
        newParts.push(part);
      }
    }
    parts = newParts;
  }
  
  return parts.filter(Boolean).map(p => p.trim());
}

/**
 * Extract invoice summary data from PDF text
 */
function extractInvoiceSummary(text) {
  const lines = text.split('\n').map(line => line.trim());
  const invoiceSummary = {};

  // Helper function to extract data using different patterns
  const extractValue = (pattern, valueIndex = 1) => {
    for (let i = 0; i < lines.length; i++) {
      const match = lines[i].match(pattern);
      if (match) {
        return match[valueIndex];
      }
    }
    return null;
  };

  // Helper function to look for pairs of lines (label + value)
  const extractLinePair = (labelPattern) => {
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].match(labelPattern)) {
        if (i + 1 < lines.length) {
          return lines[i+1]; // Return the next line
        }
      }
    }
    return null;
  };
  
  // Extract key information from the invoice
  invoiceSummary.invoiceNumber = extractLinePair(/^Invoice number$/);
  invoiceSummary.invoiceDate = extractLinePair(/^Invoice date$/);
  invoiceSummary.whatsAppAccountNumber = extractLinePair(/^WhatsApp account number$/);
  invoiceSummary.deliveryPeriod = extractLinePair(/^Delivery period$/);
  invoiceSummary.paymentDueDate = extractLinePair(/^Payment due date$/);
  invoiceSummary.paymentTerms = extractLinePair(/^Payment terms$/);
  
  // Extract monetary values - these might be on the same line or separate lines
  const listPrice = extractValue(/List price\s*\$([\d,]+\.\d+)/);
  invoiceSummary.listPrice = listPrice ? parseFloat(listPrice.replace(/,/g, '')) : null;
  
  const partnerDiscount = extractValue(/Partner discount\s*-\$([\d,]+\.\d+)/);
  invoiceSummary.partnerDiscount = partnerDiscount ? parseFloat(partnerDiscount.replace(/,/g, '')) * -1 : null;
  
  const subtotal = extractValue(/Subtotal\s*\$([\d,]+\.\d+)/);
  invoiceSummary.subtotal = subtotal ? parseFloat(subtotal.replace(/,/g, '')) : null;
  
  // For tax, handle format like "Tax 0% $0.00"
  const tax = extractValue(/Tax[^$]*\$([\d,]+\.\d+)/);
  invoiceSummary.tax = tax ? parseFloat(tax.replace(/,/g, '')) : 0;
  
  const totalDue = extractValue(/Total due[^$]*\$([\d,]+\.\d+)/);
  invoiceSummary.totalDue = totalDue ? parseFloat(totalDue.replace(/,/g, '')) : null;
  
  const currency = extractLinePair(/^Currency$/);
  invoiceSummary.currency = currency || "USD";

  return invoiceSummary;
}

/**
 * Parse account items from text
 */
function parseAccountItems(text) {
  const lines = text.split('\n').map(l => l.trim()).filter(Boolean);
  const accounts = [];
  let currentAccount = null;
  let processingItems = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // Check for "Account summary" section header
    if (line === 'Account summary') {
      processingItems = false;
      continue;
    }
    
    // Look for account header (name / account number)
    const accountMatch = line.match(/^([^/]+)\s*\/\s*(\d+)\s*$/);
    if (accountMatch) {
      // Save previous account if exists
      if (currentAccount) {
        accounts.push(currentAccount);
      }
      
      // Create new account
      currentAccount = {
        name: accountMatch[1].trim(),
        accountNumber: accountMatch[2],
        items: [],
        totalUSD: 0
      };
      
      processingItems = true;
      continue;
    }
    
    if (!currentAccount) continue;
    
    // Check for total line (end of account section)
    // Pattern: looking for standalone dollar amount like "$XX.XX"
    if (line.startsWith('$') && !line.includes('--')) {
      const amountMatch = line.match(/\$([\d,]+\.\d+)/);
      if (amountMatch) {
        currentAccount.totalUSD = parseFloat(amountMatch[1].replace(/,/g, ''));
        console.log(`Found total for ${currentAccount.name}: ${currentAccount.totalUSD}`);
        
        // Skip next line if it's "USD"
        if (i + 1 < lines.length && lines[i + 1] === "USD") {
          i++;
        }
        
        processingItems = false;
        continue;
      }
    }
    
    // Check for item lines if we're processing items
    if (processingItems) {
      // Format is typically: [Region] - [Type] List [Conversations] -- $[Amount]
      if (line.includes('List') && line.includes('--')) {
        try {
          // Try direct pattern match first (more reliable)
          const directMatch = line.match(/^(.+?)\s*-\s*(Marketing|Utility)\s*List\s*(\d[\d,]*)\s*--\s*\$([\d,.]+)$/);
          
          if (directMatch) {
            const [_, region, type, conversationsStr, totalStr] = directMatch;
            
            const item = {
              region: region.trim(),
              type: type.trim(),
              priceType: 'List',
              conversations: parseInt(conversationsStr.replace(/,/g, '')),
              total: parseFloat(totalStr)
            };
            
            currentAccount.items.push(item);
            continue;
          }
          
          // Fallback to column-based extraction
          const parts = splitByDelimiters(line, ['-', 'List', '--']);
          
          if (parts.length >= 3) {
            const region = parts[0].trim();
            const type = parts[1].trim();
            
            // Extract conversations (number before --)
            const conversationsPart = parts[2].trim();
            const conversations = parseInt(conversationsPart.replace(/,/g, ''));
            
            // Extract amount (after $)
            const amountMatch = line.match(/\$([\d,.]+)$/);
            const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
            
            currentAccount.items.push({
              region,
              type,
              priceType: 'List',
              conversations,
              total: amount
            });
          }
        } catch (err) {
          console.error('Error parsing line:', line, err);
        }
      }
    }
  }
  
  // Add the last account if exists
  if (currentAccount) {
    accounts.push(currentAccount);
  }
  
  return accounts;
}

/**
 * Main function to parse invoice PDF text
 */
const parseInvoiceText = (text) => {
  // Extract invoice summary
  const invoiceSummary = extractInvoiceSummary(text);
  
  // Extract accounts and their items
  const accounts = parseAccountItems(text);
  
  // Calculate account totals from items if not already set
  accounts.forEach(account => {
    if (account.totalUSD === 0 && account.items.length > 0) {
      account.totalUSD = parseFloat(
        account.items.reduce((sum, item) => sum + (item.total || 0), 0).toFixed(2)
      );
      console.log(`Calculated total for ${account.name} from items: ${account.totalUSD}`);
    }
  });
  
  // Calculate invoice total
  const invoiceTotal = parseFloat(
    accounts.reduce((sum, acc) => sum + (acc.totalUSD || 0), 0).toFixed(2)
  );
  
  console.log('📊 Account count:', accounts.length);
  console.log('📊 Accounts with totals:', accounts.filter(a => a.totalUSD > 0).length);
  console.log('📊 Calculated invoice total from accounts:', invoiceTotal);
  console.log('📊 Expected total from invoice summary:', invoiceSummary.totalDue);
  
  // Convert to canonical format
  const output = {
    invoiceSummary: {
      invoiceNumber: invoiceSummary.invoiceNumber || "unknown",
      invoiceDate: invoiceSummary.invoiceDate || "unknown",
      listPrice: invoiceSummary.listPrice || 0,
      partnerDiscount: invoiceSummary.partnerDiscount || 0,
      subtotal: invoiceSummary.subtotal || 0,
      tax: invoiceSummary.tax || null,
      totalDue: invoiceSummary.totalDue || 0
    },
    accounts,
    invoiceTotal,
    valid: invoiceTotal > 0 && Math.abs(invoiceTotal - (invoiceSummary.totalDue || 0)) < 0.01
  };
  
  // Additional validation step
  if (!output.valid && invoiceTotal > 0 && invoiceSummary.totalDue > 0) {
    console.log('⚠️ Validation failed: calculated total', invoiceTotal, 'vs expected', invoiceSummary.totalDue);
    console.log('⚠️ Difference:', Math.abs(invoiceTotal - invoiceSummary.totalDue));
    
    // If the difference is less than 1% of the total, consider it valid
    // This handles rounding differences that might occur
    if (Math.abs(invoiceTotal - invoiceSummary.totalDue) / invoiceSummary.totalDue < 0.01) {
      console.log('✅ Total within 1% tolerance - marking as valid');
      output.valid = true;
    }
  }
  
  return output;
};

export {
  parseInvoiceText
};