{"name": "whatsapp-invoice-extractor", "version": "1.0.0", "description": "Service to extract information from WhatsApp PDF invoices and convert it to JSON", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --watch src/index.js"}, "keywords": ["pdf", "json", "extractor", "whatsapp", "invoice"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.0", "crypto-js": "^4.2.0", "dotenv": "^16.0.3", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.5.0", "pdf-parse": "^1.1.1"}, "devDependencies": {"nodemon": "^2.0.22"}}