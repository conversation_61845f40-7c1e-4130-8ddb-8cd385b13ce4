import { Model, DataTypes } from 'sequelize'
import sequelize from '../init'
import Language from './language'

class Translation extends Model {
  public entityId!: number
  public entityType!: string
  public field!: string
  public languageId!: number
  public translation!: string
}

Translation.init(
  {
    entityId: DataTypes.INTEGER,
    entityType: DataTypes.STRING,
    field: DataTypes.STRING,
    languageId: DataTypes.INTEGER,
    translation: DataTypes.TEXT,
  },
  {
    sequelize,
    modelName: 'Translation',
    scopes: {
      withAssociations: {
        include: [
          {
            model: Language,
            as: 'language',
            attributes: ['name', 'code'],
          },
        ],
      },
    },
  }
)

export default Translation
