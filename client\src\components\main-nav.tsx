import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

export function MainNav({
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  const location = useLocation()
  const { t } = useTranslation()

  const getLinkClass = (path: string) => {
    return location.pathname === path ||
      (path !== '/' && location.pathname.includes(path))
      ? 'text-sm font-medium transition-colors text-primary uppercase'
      : 'text-sm font-medium text-muted-foreground transition-colors hover:text-primary uppercase font-bold'
  }

  return (
    <nav
      className={cn(
        'flex items-center space-x-2 md:space-x-4 lg:space-x-6',
        className
      )}
      {...props}
    >
      <Link to="/" className={getLinkClass('/')}>
        {t('home')}
      </Link>
      <Link to="/clients" className={getLinkClass('/clients')}>
        {t('clients')}
      </Link>
      <Link to="/counters" className={getLinkClass('/counters')}>
        {t('counters')}
      </Link>
    </nav>
  )
}
