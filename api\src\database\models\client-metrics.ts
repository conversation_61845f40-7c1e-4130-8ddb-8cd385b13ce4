import { Model, DataTypes } from 'sequelize'
import sequelize from '../init'
import Client from './client'

class ClientMetrics extends Model {
  public id!: number
  public clientId!: number
  public moduleId!: number
  public dateFrom!: Date
  public dateTo!: Date
  public metricName!: string
  public metricValue!: number
  public reference!: number
}

ClientMetrics.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    clientId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'clients',
        key: 'id',
      },
    },
    moduleId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'modules',
        key: 'id',
      },
    },
    dateFrom: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    dateTo: {
      type: DataTypes.DATEONLY,
    },
    metricName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    metricValue: {
      type: DataTypes.BIGINT,
      allowNull: false,
    },
    reference: {
      type: DataTypes.BIGINT,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'ClientMetrics',
    tableName: 'client_metrics',
    scopes: {
      withAssociations: {
        include: [
          { model: Client, as: 'client', attributes: ['id', 'name'] },
        ],
      },
    },
  }
)

export default ClientMetrics
