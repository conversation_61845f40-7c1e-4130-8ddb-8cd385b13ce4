import { Request, Response, NextFunction } from 'express'
import Client from '../database/models/client'

const allowMetricsRequestToClient = async (req: Request, res: Response, next: NextFunction) => {
  const { role, clientName } = req
  if (!role || role === 'client') {
    const { clientIds } = req.query
    console.log(typeof clientIds)
    if (typeof clientIds === 'string') {
      const clientIdsArray = clientIds
        .split(',')
        .map((id) => parseInt(id.trim(), 10))
        .filter((id) => !isNaN(id))

      if (clientIdsArray.length > 1) {
        return res
          .status(400)
          .json({ message: 'You cannot query multiple clients' })
      }

      const client = await Client.findByPk(clientIdsArray[0])
      if (client.name !== clientName) {
        return res
          .status(403)
          .json({ message: 'You cannot query this client' })
      }

      // Si llegamos aquí, la validación fue exitosa para usuarios client
      return next()
    } else {
      return res
        .status(400)
        .json({ message: 'You must provide a client id' })
    }
  }
  // Para usuarios admin/viewer, continuar sin validación adicional
  next()
}

export { allowMetricsRequestToClient }
