import { Router, Request, Response } from 'express'

import Translation from '../database/models/translation'

const router = Router()

router.get('/', async (req: Request, res: Response) => {
  try {
    const { entityType } = req.query
    const entities = await Translation.scope(
      'withAssociations'
    ).findAll({
      where: { entityType },
    })
    res.json(entities)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

export default router
