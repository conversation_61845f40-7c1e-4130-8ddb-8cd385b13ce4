trigger:
  branches:
    include:
      - refs/tags/version/*

pool:
  name: 'devops-agent'

variables:
- group: yUsage-PRD
  
stages:
- stage: BuildPush
  displayName: 'Build and Push Docker Images'
  jobs:
  - job: BuildPushAPI
    displayName: 'Build and Push API Docker Image'
    pool:
      name: 'devops-agent'
    steps:
    - script: |
        docker system prune -af
      displayName: 'Clean Docker Cache'

    - task: Docker@2
      displayName: 'Build and Push API Docker Image'
      inputs:
        command: buildAndPush
        repository: $(dockerHubRepoBackend)
        Dockerfile: api/Dockerfile.prod
        tags: |
          $(Build.SourceBranchName)
        containerRegistry: $(containerRegistry)

  - job: BuildPushClient
    displayName: 'Build and Push Client Docker Image'
    pool:
      name: 'devops-agent'
    steps:
    - script: |
        docker system prune -af
      displayName: 'Clean Docker Cache'

    - task: Docker@2
      displayName: 'Build and Push Client Docker Image'
      inputs:
        command: buildAndPush
        repository: $(dockerHubRepoFrontend)
        Dockerfile: client/Dockerfile.prod
        tags: |
          $(Build.SourceBranchName)
        containerRegistry: $(containerRegistry)
