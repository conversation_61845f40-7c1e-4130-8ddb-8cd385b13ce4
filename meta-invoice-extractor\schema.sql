-- MySQL schema for WhatsApp invoice data

-- Create database if it doesn't exist
-- CREATE DATABASE IF NOT EXISTS whatsapp_invoices;
-- USE whatsapp_invoices;

-- Invoice summary table
CREATE TABLE IF NOT EXISTS invoice_summaries (
  id INT AUTO_INCREMENT PRIMARY KEY,
  invoice_id VARCHAR(20) NOT NULL,
  invoice_date DATE,
  list_price DECIMAL(12,2),
  partner_discount DECIMAL(12,2),
  subtotal DECIMAL(12,2),
  tax DECIMAL(12,2) NULL,
  total_due DECIMAL(12,2),
  created_at DATETIME NOT NULL,
  updated_at DATETIME NULL,
  UNIQUE KEY unique_invoice_id (invoice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Invoice accounts table
CREATE TABLE IF NOT EXISTS invoice_accounts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  invoice_id VARCHAR(20) NOT NULL,
  account_name VARCHAR(100) NOT NULL,
  account_number VARCHAR(30) NOT NULL,
  total_usd DECIMAL(12,2) NOT NULL,
  created_at DATETIME NOT NULL,
  updated_at DATETIME NULL,
  UNIQUE KEY unique_account_invoice (invoice_id, account_number),
  FOREIGN KEY (invoice_id) REFERENCES invoice_summaries(invoice_id)
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Invoice account line items
CREATE TABLE IF NOT EXISTS invoice_account_items (
  id INT AUTO_INCREMENT PRIMARY KEY,
  account_id INT NOT NULL,
  invoice_id VARCHAR(20) NOT NULL,
  region VARCHAR(50) NOT NULL,
  item_type VARCHAR(20) NOT NULL,
  price_type VARCHAR(20) NOT NULL,
  conversations INT NOT NULL,
  total DECIMAL(12,2) NOT NULL,
  created_at DATETIME NOT NULL,
  FOREIGN KEY (account_id) REFERENCES invoice_accounts(id)
    ON DELETE CASCADE,
  FOREIGN KEY (invoice_id) REFERENCES invoice_summaries(invoice_id)
    ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create indices for faster queries
CREATE INDEX idx_invoice_date ON invoice_summaries(invoice_date);
CREATE INDEX idx_account_name ON invoice_accounts(account_name);
CREATE INDEX idx_region_type ON invoice_account_items(region, item_type);

-- Add views for common queries

-- View for invoice totals by account
CREATE OR REPLACE VIEW invoice_account_totals AS
SELECT 
  is.invoice_id,
  is.invoice_date,
  ia.account_name,
  ia.account_number,
  ia.total_usd,
  is.total_due as invoice_total
FROM 
  invoice_summaries is
JOIN 
  invoice_accounts ia ON is.invoice_id = ia.invoice_id
ORDER BY 
  is.invoice_date DESC, ia.account_name;

-- View for account items by invoice
CREATE OR REPLACE VIEW account_items_by_invoice AS
SELECT 
  is.invoice_id,
  is.invoice_date,
  ia.account_name,
  ia.account_number,
  iai.region,
  iai.item_type,
  iai.price_type,
  iai.conversations,
  iai.total
FROM 
  invoice_summaries is
JOIN 
  invoice_accounts ia ON is.invoice_id = ia.invoice_id
JOIN 
  invoice_account_items iai ON ia.id = iai.account_id
ORDER BY 
  is.invoice_date DESC, ia.account_name, iai.region, iai.item_type;

-- View for total conversations by region and type
CREATE OR REPLACE VIEW conversations_by_region_type AS
SELECT 
  region,
  item_type,
  SUM(conversations) as total_conversations,
  SUM(total) as total_amount
FROM 
  invoice_account_items
GROUP BY 
  region, item_type
ORDER BY 
  region, item_type;