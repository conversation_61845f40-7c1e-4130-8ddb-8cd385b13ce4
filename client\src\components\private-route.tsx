import React, { useContext } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { AuthContext } from '../context/authContext'
import Loader from './ui/loader'

interface PrivateRouteProps {
  element: React.ReactElement
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({ element }) => {
  const { isLoggedIn, loading, getUserInfo } = useContext(AuthContext)
  const location = useLocation()
  const { role } = getUserInfo()
  const isClient = role !== 'admin' && role !== 'viewer'

  if (loading) {
    return <Loader />
  }

  if (!isLoggedIn) return <Navigate to="/auth/signin" />

  if (isClient && location.pathname !== '/')
    return <Navigate to="/" />

  return element
}

export default PrivateRoute
