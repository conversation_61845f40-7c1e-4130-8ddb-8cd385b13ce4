// pdf-parse-override.mjs
import * as fs from 'fs';
import { createRequire } from 'module';

// Create a require function
const require = createRequire(import.meta.url);

// Use require to import the CommonJS module
const originalPdfParse = require('pdf-parse');

// Create a wrapper function
function pdfParseWrapper(dataBuffer, options) {
  return originalPdfParse(dataBuffer, options);
}

// Copy all properties
Object.assign(pdfParseWrapper, originalPdfParse);

// Export as default
export default pdfParseWrapper;