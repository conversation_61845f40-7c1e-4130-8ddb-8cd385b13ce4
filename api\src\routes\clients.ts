import { Router, Request, Response } from 'express'
import Client from '../database/models/client'
import ClientModule from '../database/models/client-modules'
import ClientCounter from '../database/models/client-counters'
import sequelize from '../database/init'
import {
  allowRequestToAdmin,
  allowRequestToViewer,
} from '../middleware/permissionsMiddleware'
import clientFilterMiddleware from '../middleware/clientFilterMiddleware'

const router = Router()

interface QueryCondition {
  where?: {
    isTestClient?: boolean
    name?: string
  }
  order?: Array<Array<string>>
}

router.get('/', clientFilterMiddleware, async (req: Request, res: Response) => {
  try {
    const { role, clientName, clientId } = req

    const filterTestClients: boolean =
      req.query.filterTestClients === 'true'

    const baseQueryCondition: QueryCondition = {}

    if (filterTestClients) {
      baseQueryCondition.where = { isTestClient: false }
    }

    // Para usuarios con rol 'client', filtrar solo su cliente
    if (role === 'client') {
      if (!baseQueryCondition.where) {
        baseQueryCondition.where = {}
      }
      // Usar clientId en lugar de clientName para mayor seguridad
      baseQueryCondition.where = {
        ...baseQueryCondition.where,
        id: clientId
      }
    }

    const clients = await Client.scope('withAssociations').findAll({
      ...baseQueryCondition,
      order: [['name', 'ASC']],
    })

    res.json(clients)
  } catch (error) {
    console.error('Failed to fetch clients:', error)
    res
      .status(500)
      .json({ error: 'An error occurred while fetching clients.' })
  }
})

router.get(
  '/:id',
  allowRequestToViewer,
  async (req: Request, res: Response) => {
    try {
      const client = await Client.scope('withAssociations').findByPk(
        req.params.id
      )
      res.json(client)
    } catch (error) {
      res
        .status(500)
        .json({ error: error.message || error.toString() })
    }
  }
)

router.put('/:id', allowRequestToAdmin, async (req, res) => {
  const transaction = await sequelize.transaction()
  try {
    const { id } = req.params
    const { activeModules, activeCounters, ...updateFields } =
      req.body

    const [updateCount] = await Client.update(updateFields, {
      where: { id },
      transaction,
    })

    if (updateCount === 0) {
      await transaction.rollback()
      return res.status(404).json({ message: 'Client not found' })
    }

    await ClientModule.destroy({
      where: { clientId: id },
      transaction,
    })

    const newModuleAssociations = activeModules.map((moduleId) => ({
      clientId: id,
      moduleId,
    }))
    await ClientModule.bulkCreate(newModuleAssociations, {
      transaction,
    })

    await ClientCounter.destroy({
      where: { clientId: id },
      transaction,
    })

    const newCounterAssociations = activeCounters.map(
      (counterId) => ({
        clientId: id,
        counterId,
      })
    )
    await ClientCounter.bulkCreate(newCounterAssociations, {
      transaction,
    })

    await transaction.commit()

    const updatedClient = await Client.scope(
      'withAssociations'
    ).findByPk(id)

    res.json(updatedClient)
  } catch (error) {
    if (transaction) await transaction.rollback()
    res.status(500).json({ error: error.message || error.toString() })
  }
})

export default router
