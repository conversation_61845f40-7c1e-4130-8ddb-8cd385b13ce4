import React, { useEffect, useContext } from 'react'
import classNames from 'classnames'
import type { DraggableSyntheticListeners } from '@dnd-kit/core'
import type { Transform } from '@dnd-kit/utilities'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import styles from './Item.module.css'
import Counter from '@/types/counter'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { AppContext } from '@/context/appContext'
import { TranslatableEntity, getTranslation } from '@/lib/utils'

export interface Props {
  dragOverlay?: boolean
  color?: string
  disabled?: boolean
  dragging?: boolean
  handle?: boolean
  handleProps?: any
  height?: number
  index?: number
  fadeIn?: boolean
  transform?: Transform | null
  listeners?: DraggableSyntheticListeners
  sorting?: boolean
  style?: React.CSSProperties
  transition?: string | null
  wrapperStyle?: React.CSSProperties
  value: React.ReactNode
  counters: Counter[] | undefined
  onRemove?(): void
  renderItem?(args: {
    dragOverlay: boolean
    dragging: boolean
    sorting: boolean
    index: number | undefined
    fadeIn: boolean
    listeners: DraggableSyntheticListeners
    ref: React.Ref<HTMLElement>
    style: React.CSSProperties | undefined
    transform: Props['transform']
    transition: Props['transition']
    value: Props['value']
  }): React.ReactElement
}

export const Item = React.memo(
  React.forwardRef<HTMLLIElement, Props>(
    (
      {
        color,
        dragOverlay,
        dragging,
        disabled,
        fadeIn,
        handle,
        handleProps,
        height,
        index,
        listeners,
        onRemove,
        renderItem,
        sorting,
        style,
        transition,
        transform,
        value,
        wrapperStyle,
        counters,
        ...props
      },
      ref
    ) => {
      useEffect(() => {
        if (!dragOverlay) {
          return
        }

        document.body.style.cursor = 'grabbing'

        return () => {
          document.body.style.cursor = ''
        }
      }, [dragOverlay])

      const { language } = useContext(AppContext)

      const getCounterData = (): Counter | undefined => {
        return counters?.find((counter) => counter.name === value)
      }

      const navigate = useNavigate()

      const { id, name, code, type } = getCounterData() || {}
      const { t } = useTranslation()

      const counterTranslation = getTranslation(
        language,
        'counter',
        {
          id,
          translations: getCounterData()?.translations,
        } as TranslatableEntity,
        'name'
      )

      const valueToRender = counterTranslation
        ? counterTranslation.translation
        : name

      return renderItem ? (
        renderItem({
          dragOverlay: Boolean(dragOverlay),
          dragging: Boolean(dragging),
          sorting: Boolean(sorting),
          index,
          fadeIn: Boolean(fadeIn),
          listeners,
          ref,
          style,
          transform,
          transition,
          value,
        })
      ) : (
        <li
          className={classNames(
            styles.Wrapper,
            fadeIn && styles.fadeIn,
            sorting && styles.sorting,
            dragOverlay && styles.dragOverlay
          )}
          style={
            {
              ...wrapperStyle,
              transition: [transition, wrapperStyle?.transition]
                .filter(Boolean)
                .join(', '),
              '--translate-x': transform
                ? `${Math.round(transform.x)}px`
                : undefined,
              '--translate-y': transform
                ? `${Math.round(transform.y)}px`
                : undefined,
              '--scale-x': transform?.scaleX
                ? `${transform.scaleX}`
                : undefined,
              '--scale-y': transform?.scaleY
                ? `${transform.scaleY}`
                : undefined,
              '--index': index,
              '--color': color,
              transform: `translate(${
                transform ? Math.round(transform.x) : 0
              }px, ${
                transform ? Math.round(transform.y) : 0
              }px) scale(${transform?.scaleX || 1}, ${
                transform?.scaleY || 1
              })`,
              transformOrigin: 'center center',
            } as React.CSSProperties
          }
          ref={ref}
        >
          <div className="flex items-center justify-between w-full bg-card-item rounded-lg">
            <div
              className={classNames(
                styles.Item,
                dragging && styles.dragging,
                handle && styles.withHandle,
                dragOverlay && styles.dragOverlay,
                disabled && styles.disabled,
                color && styles.color,
                'text-white'
              )}
              style={style}
              data-cypress="draggable-item"
              {...(!handle ? listeners : undefined)}
              {...props}
              tabIndex={!handle ? 0 : undefined}
            >
              <div className="flex items-center justify-between w-full ">
                <div className="font-bold">
                  {valueToRender}{' '}
                  <span className="font-medium italic text-sm">
                    ({code})
                  </span>
                  <Badge className="ml-2">{type?.name}</Badge>
                </div>
              </div>
            </div>

            <Button
              variant="ghost"
              className="mr-4"
              onClick={() => navigate(`/counters/${id}`)}
            >
              {t('edit')}
            </Button>
          </div>
        </li>
      )
    }
  )
)
