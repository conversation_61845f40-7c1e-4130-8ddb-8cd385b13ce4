const countersExamples = [
  {
    name: 'Agentes Simultáneos',
    description: 'Agentes Simultáneos',
    active: true,
    code: 'max_concurrent_agents',
    type_id: 1,
    group_id: 1,
    order: 1,
    unit_id: 1,
  },
  {
    name: 'Agentes nominales',
    description: 'Agentes nominales',
    active: true,
    code: 'peek_logged_agents',
    type_id: 3,
    group_id: 1,
    order: 2,
    unit_id: 1,
  },
  {
    name: 'yFlow Storage',
    description: 'Storage yFlow',
    active: true,
    code: 'agents_snapshot',
    type_id: 2,
    group_id: 2,
    order: 1,
    unit_id: 1,
  },
  {
    name: 'Casos ySmart',
    description: 'Casos ySmart',
    active: true,
    code: 'peek_enqueued_messages',
    type_id: 3,
    group_id: 2,
    order: 2,
    unit_id: 1,
  },
  {
    name: 'Encuestas enviadas',
    description: 'Encuestas enviadas',
    active: true,
    code: 'surveys_sent',
    type_id: 3,
    group_id: 2,
    order: 3,
    unit_id: 1,
  },
  {
    name: 'Tokens GPT',
    description: 'Tokens GPT',
    active: true,
    code: 'tokens_gpt',
    type_id: 3,
    group_id: 3,
    order: 1,
    unit_id: 1,
  },
  {
    name: 'Usuarios únicos',
    description: 'Usuarios únicos',
    active: true,
    code: 'unique_users',
    type_id: 3,
    group_id: 3,
    order: 2,
    unit_id: 1,
  },
  {
    name: 'Nuevos Casos',
    description: 'Nuevos Casos',
    active: true,
    code: 'new_cases',
    type_id: 3,
    group_id: 3,
    order: 3,
    unit_id: 1,
  },
  {
    name: 'Nuevos Mensajes',
    description: 'Nuevos Mensajes',
    active: true,
    code: 'new_messages',
    type_id: 3,
    group_id: 3,
    order: 4,
    unit_id: 1,
  },
  {
    name: 'Mensajes de Pago',
    description: 'Mensajes de Pago',
    active: true,
    code: 'messages_payment',
    type_id: 3,
    group_id: 3,
    order: 5,
    unit_id: 1,
  },
  {
    name: 'Mensajes Respondidos',
    description: 'Mensajes Respondidos',
    active: true,
    code: 'replied_messages',
    type_id: 3,
    group_id: 3,
    order: 6,
    unit_id: 1,
  },
  {
    name: 'Mensajes Salientes',
    description: 'Mensajes Salientes',
    active: true,
    code: 'outbound_messages',
    type_id: 3,
    group_id: 3,
    order: 7,
    unit_id: 1,
  },
  {
    name: 'Salientes',
    description: 'Salientes',
    active: true,
    code: 'outgoing',
    type_id: 3,
    group_id: 3,
    order: 8,
    unit_id: 1,
  },
  {
    name: 'Mensajes Respondidos yFlow',
    description: 'Mensajes Respondidos yFlow',
    active: true,
    code: 'yflow_replied_messages',
    type_id: 3,
    group_id: 3,
    order: 9,
    unit_id: 1,
  },
  {
    name: 'Casos Cerrados Solo con yFlow',
    description: 'Casos Cerrados Solo con yFlow',
    active: true,
    code: 'closed_cases_only_with_yflow',
    type_id: 3,
    group_id: 3,
    order: 10,
    unit_id: 1,
  },
]

module.exports = {
  up: async (queryInterface) => {
    const existingCounters = await queryInterface.sequelize.query(
      'SELECT code FROM counters'
    )
    const existingCodes = existingCounters[0].map((counter) => counter.code)

    const newCounters = countersExamples.filter(
      (counter) => !existingCodes.includes(counter.code)
    )

    const counters = newCounters.map((counter) => ({
      created_at: new Date(),
      updated_at: new Date(),
      ...counter,
    }))

    if (counters.length > 0) {
      await queryInterface.bulkInsert('counters', counters)
    }
  },

  down: async (queryInterface) => {
    const codesToDelete = countersExamples.map((counter) => counter.code)
    await queryInterface.bulkDelete('counters', {
      code: codesToDelete,
    })
  },
}
