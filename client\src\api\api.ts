import axios from 'axios'
import { API_URL } from './constants'

const apiInstance = axios.create({
  baseURL: API_URL,
})

apiInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('yoizen_token')

    if (token) {
      config.headers.authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

export default apiInstance
