import { Model, DataTypes } from 'sequelize'
import sequelize from '../init'

class ClientCounters extends Model {
  public clientId!: number
  public counterId!: number
}

ClientCounters.init(
  {
    clientId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'clients',
        key: 'id',
      },
      field: 'client_id',
    },
    counterId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'counters',
        key: 'id',
      },
      field: 'counter_id',
    },
  },
  {
    sequelize,
    modelName: 'ClientCounters',
    tableName: 'client_counters',
  }
)

export default ClientCounters
