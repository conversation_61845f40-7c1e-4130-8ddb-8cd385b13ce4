import { fetchClients, fetchClientsId } from '@/api/clients'
import { useQuery } from '@tanstack/react-query'
import { DEFAULT_CACHE_TIME } from '@/lib/constants'

const useClients = (
  filterTestClients: boolean = false,
  queryKey: string[] = ['clients']
) => {
  return useQuery({
    queryKey,
    queryFn: () => fetchClients(filterTestClients),
    staleTime: DEFAULT_CACHE_TIME,
  })
}

const useClientsId = (id: string | undefined) => {
  return useQuery({
    queryKey: [`clientsId-${id}`],
    queryFn: () => fetchClientsId(id),
    staleTime: DEFAULT_CACHE_TIME,
  })
}

export { useClients, useClientsId }
