import { Request, Response, NextFunction } from 'express'
import Client from '../database/models/client'

/**
 * Middleware para aplicar filtrado automático por cliente
 * - Para usuarios con rol 'client': aplica filtrado automático por su clientId
 * - Para usuarios admin/viewer: permite acceso a todos los clientes o al cliente seleccionado
 */
const clientFilterMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const { role, clientId, clientName } = req

    // Para usuarios con rol 'client', validar que el cliente existe y está activo
    if (role === 'client') {
      if (!clientId || !clientName) {
        return res.status(403).json({ 
          message: 'Client access denied - missing client information.' 
        })
      }

      // Verificar que el cliente existe y está activo
      const client = await Client.findOne({
        where: { 
          id: clientId, 
          name: clientName, 
          active: true 
        }
      })

      if (!client) {
        return res.status(403).json({ 
          message: 'Client access denied - client not found or inactive.' 
        })
      }

      // Agregar el clientId a los parámetros de consulta para filtrado automático
      req.query.clientIds = String(clientId)
    }

    // Para admin/viewer, si tienen un cliente seleccionado, agregarlo a la consulta
    if ((role === 'admin' || role === 'viewer') && clientId) {
      // Verificar que el cliente seleccionado existe y está activo
      const client = await Client.findOne({
        where: { 
          id: clientId, 
          active: true 
        }
      })

      if (!client) {
        return res.status(400).json({ 
          message: 'Selected client not found or inactive.' 
        })
      }

      // Si no hay clientIds en la consulta, usar el cliente seleccionado
      if (!req.query.clientIds) {
        req.query.clientIds = String(clientId)
      }
    }

    next()
  } catch (error) {
    console.error('Error in client filter middleware:', error)
    res.status(500).json({ message: 'Internal server error.' })
  }
}

export default clientFilterMiddleware
