@import url('https://fonts.googleapis.com/css2?family=Barlow:wght@400;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    --gradient: linear-gradient(to top left, #4AC29A, #BDFFF3);


    /* Nuevos gradients basasados en los nuevos colores*/
    --custom-gradient: linear-gradient(52deg, rgba(26, 102, 255, 1) 0%, rgba(74, 58, 191, 1) 34%, rgba(149, 76, 125, 1) 85%, rgba(253, 100, 33, 1) 99%);
    --custom-gradient-opacity: linear-gradient(52deg, rgba(26, 102, 255, 0.1) 0%, rgba(74, 58, 191, 0.1) 34%, rgba(149, 76, 125, 0.1) 85%, rgba(253, 100, 33, 0.1) 99%);


    --background: 0 0% 0%;
    --foreground: 170 4.9% 97.65%;

    --muted: 227 15% 18%;
    --muted-foreground: 170 4.9% 55.3%;


    --popover: 216 5% 32%;
    --popover-foreground:0 0% 100%;


    --card: 216 5% 32%;
    --card-foreground: 0 0% 100%;

    --metrics-card: linear-gradient(52deg, rgba(26, 102, 255, 1) 0%, rgba(74, 58, 191, 1) 34%, rgba(149, 76, 125, 1) 85%, rgba(253, 100, 33, 1) 99%);
    --metrics-card-foreground: 0 0% 100%;

    --border: 0 0% 0%;
    --input: 227 15% 18%;


    --primary: 221 100% 55%;
    --primary-foreground: 170 4.9% 5.300000000000001%;


    --secondary: 227 15% 18%;
    --secondary-foreground: 221, 100%, 55%, 0.5;
    --accent: 227 15% 18%;
    --accent-foreground: 221, 100%, 55%, 0.5;

    --destructive: 216 5% 32%;
    --destructive-foreground: 221, 100%, 55%, 0.5;


    --ring: 221 100% 55%;

    --radius: 0.5rem;


    --card-item: 227 15% 18%;
    --warning: 247.68 39.5% 66.85%;
  }


  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 221, 100%, 55%, 0.5;
    --ring: 240 4.9% 83.9%;
  }


  .font-barlow {
    font-family: 'Barlow', sans-serif;
  }
}


@layer base {
  * {
    @apply border-border;
  }


  body {
    @apply bg-background text-foreground;
  }


  .custom-gradient-background {
    background: rgb(26, 102, 255);
    background: var(--custom-gradient);
  }
}

/* Probar estilos para las tablas  */
.table-fixed {
	table-layout: fixed;
	width: 100%;
  }

  .table-fixed th,
  .table-fixed td {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding: 0.75rem 1rem;
  }

  .badge-container {
	display: flex;
	flex-wrap: wrap;
	gap: 0.25rem;
	max-width: 100%;
  }

  .badge-container .badge {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 150px;
  }

  .table-row-hover:hover {
	background-color: rgba(var(--muted), 0.05);
  }

  .pagination {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	gap: 0.5rem;
	margin-top: 1rem;
  }
