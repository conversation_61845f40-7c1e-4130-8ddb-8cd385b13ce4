const allowRequestToAdmin = (req, res, next) => {
  if (req && req.role !== 'admin') {
    return res
      .status(403)
      .json({ message: 'You do not have access to this resource.' })
  }
  next()
}

const allowRequestToViewer = (req, res, next) => {
  if (req && req.role !== 'admin' && req.role !== 'viewer') {
    return res
      .status(403)
      .json({ message: 'You do not have access to this resource.' })
  }
  next()
}

export { allowRequestToAdmin, allowRequestToViewer }
