import {
  SplitText,
  WordWrapperProp,
  LineWrapperProp,
} from '@cyriacbr/react-split-text'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { memo } from 'react'
import yoizenLogo from '/yoizenlogo.png';

const WordWrapper: React.FC<WordWrapperProp> = ({
  children,
  wordIndex,
}) => {
  return (
    <motion.span
      transition={{
        ease: 'easeOut',
        duration: 1,
        delay: 0.05 * wordIndex, // Adjusted for a smoother animation
      }}
      initial={{ y: '100%' }}
      animate={{ y: '0%' }}
      style={{ display: 'inline-block', whiteSpace: 'pre' }}
    >
      {children}
    </motion.span>
  )
}

const LineWrapper: React.FC<LineWrapperProp> = ({ children }) => {
  return (
    <h2
      className="text-h2 font-h2 text-white"
      style={{ overflow: 'hidden', display: 'block' }}
    >
      {children}
    </h2>
  )
}

const MemoizedWordWrapper = memo(WordWrapper)
const MemoizedLineWrapper = memo(LineWrapper)

const Raise: React.FC = () => {
  const { t } = useTranslation()

  return (
    <div className="space-y-4 text-center"> 
      <img src={yoizenLogo} alt="Yoizen" className="pb-8 mx-auto w-64" /> 
      <SplitText
        WordWrapper={MemoizedWordWrapper}
        LineWrapper={MemoizedLineWrapper}
      >
        {t('metricsConsole')}
      </SplitText>
    </div>
  )
}

export default Raise
