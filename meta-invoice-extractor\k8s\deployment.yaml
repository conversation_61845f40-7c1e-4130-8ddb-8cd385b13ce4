apiVersion: apps/v1
kind: Deployment
metadata:
  name: meta-invoice-extractor
  labels:
    app: meta-invoice-extractor
spec:
  replicas: 2
  selector:
    matchLabels:
      app: meta-invoice-extractor
  template:
    metadata:
      labels:
        app: meta-invoice-extractor
    spec:
      containers:
      - name: meta-invoice-extractor
        image: your-registry/meta-invoice-extractor:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        resources:
          requests:
            cpu: "100m"
            memory: "256Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        env:
        - name: PORT
          value: "3000"
        - name: NODE_ENV
          value: "production"
        - name: INPUT_DIR
          value: "/app/data/input"
        - name: OUTPUT_DIR
          value: "/app/data/output"
        - name: PROCESSED_DIR
          value: "/app/data/processed"
        - name: ERROR_DIR
          value: "/app/data/error"
        - name: USE_ADDITIONAL_REPOSITORIES
          value: "true"
        - name: MYSQL_HOST
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: mysql-host
        - name: MYSQL_USER
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: mysql-user
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: mysql-password
        - name: MYSQL_DATABASE
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: mysql-database
        - name: WHATSAPP_DB_HOST
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: whatsapp-db-host
        - name: WHATSAPP_DB_USER
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: whatsapp-db-user
        - name: WHATSAPP_DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: whatsapp-db-password
        - name: WHATSAPP_DB_NAME
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: whatsapp-db-name
        - name: CENTRALIZER_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: centralizer-endpoint
        - name: CENTRALIZER_AUTH_SALT
          valueFrom:
            secretKeyRef:
              name: meta-invoice-secrets
              key: centralizer-auth-salt
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: invoice-data
          mountPath: /app/data
      volumes:
      - name: invoice-data
        persistentVolumeClaim:
          claimName: meta-invoice-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: meta-invoice-extractor
spec:
  selector:
    app: meta-invoice-extractor
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: v1
kind: Secret
metadata:
  name: meta-invoice-secrets
type: Opaque
data:
  mysql-host: BASE64_ENCODED_MYSQL_HOST
  mysql-user: BASE64_ENCODED_MYSQL_USER
  mysql-password: BASE64_ENCODED_MYSQL_PASSWORD
  mysql-database: BASE64_ENCODED_MYSQL_DATABASE
  whatsapp-db-host: BASE64_ENCODED_WHATSAPP_DB_HOST
  whatsapp-db-user: BASE64_ENCODED_WHATSAPP_DB_USER
  whatsapp-db-password: BASE64_ENCODED_WHATSAPP_DB_PASSWORD
  whatsapp-db-name: BASE64_ENCODED_WHATSAPP_DB_NAME
  centralizer-endpoint: BASE64_ENCODED_CENTRALIZER_ENDPOINT
  centralizer-auth-salt: BASE64_ENCODED_CENTRALIZER_AUTH_SALT