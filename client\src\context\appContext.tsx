import { ReactNode, createContext, useState, useEffect } from 'react'
import i18n from 'i18next'
import { DateRange } from 'react-day-picker'
import Metric from '@/types/metric'

interface AppContextType {
  language: string | null
  changeLanguage: (newLanguage: string) => void
  clientIds: number[]
  dateRange: DateRange | undefined
  metrics: Metric[] | undefined
  changeMetricsSearch: (
    newClientIds: number[],
    newDateRange: DateRange,
    metrics: Metric[] | undefined
  ) => void
  clearContext: () => void
}

const defaultAppContext: AppContextType = {
  language: 'es',
  changeLanguage: () => {},
  clientIds: [],
  dateRange: undefined,
  metrics: [],
  changeMetricsSearch: () => {},
  clearContext: () => {},
}

export const AppContext =
  createContext<AppContextType>(defaultAppContext)

interface AppProviderProps {
  children: ReactNode
}

const AppProvider: React.FC<AppProviderProps> = ({ children }) => {
  const [language, setLanguage] = useState(
    localStorage.getItem('language')
  )

  const [clientIds, setClientIds] = useState<number[]>(
    localStorage.getItem('client_ids')
      ? localStorage
          .getItem('client_ids')
          ?.split(',')
          .map((id) => parseInt(id, 10)) || []
      : []
  )

  const ctxDateRange = localStorage.getItem('date_range') || ''

  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    ctxDateRange ? JSON.parse(ctxDateRange) : undefined
  )

  const [metrics, setMetrics] = useState<Metric[] | undefined>([])

  useEffect(() => {
    const detectAndSetLanguage = () => {
      const supportedLanguages = ['en', 'es', 'pt']

      let userLanguage = language || navigator.language?.split('-')[0]

      userLanguage = supportedLanguages.includes(userLanguage)
        ? userLanguage
        : 'en'

      setLanguage(userLanguage)
      i18n.changeLanguage(userLanguage)

      localStorage.setItem('language', userLanguage)
    }

    detectAndSetLanguage()
  }, [language])

  const changeMetricsSearch = (
    newClientIds: number[],
    newDateRange: DateRange,
    metrics: Metric[] | undefined
  ) => {
    if (newDateRange && newDateRange.from && newDateRange.to) {
      setDateRange(newDateRange)
      localStorage.setItem('date_range', JSON.stringify(newDateRange))
    }
    setClientIds(newClientIds)
    setMetrics(metrics)
    localStorage.setItem('client_ids', newClientIds.join(','))
  }

  const changeLanguage = (newLanguage: string) => {
    i18n.changeLanguage(newLanguage)
    setLanguage(newLanguage)
  }

  const clearContext = () => {
    setClientIds([])
    setDateRange(undefined)
    setMetrics([])
    localStorage.removeItem('client_ids')
    localStorage.removeItem('date_range')
  }

  return (
    <AppContext.Provider
      value={{
        language,
        changeLanguage,
        clientIds,
        dateRange,
        changeMetricsSearch,
        metrics,
        clearContext,
      }}
    >
      {children}
    </AppContext.Provider>
  )
}

export { AppProvider }
