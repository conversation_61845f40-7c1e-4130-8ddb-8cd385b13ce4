import path from 'path';
import fs from 'fs';

import { parseInvoiceText ,generateAccountSummaries } from './exportFunctions.js';
import DataReporter from '../dataReporter.js';
import directoryManager, { invoicesDir, processedDir, errorDir, outputDir } from '../directoryManager.js';

import pdfParse from './pdf-parse-override.mjs';

/**
 * Process a PDF invoice file
 * @param {string} filename - The name of the PDF file to process
 * @returns {Promise<boolean>} - True if processing was successful
 */
async function processInvoicePdf(filename) {
  const filePath = path.join(invoicesDir, filename);
  
  try {
    console.log(`Processing invoice PDF: ${filename}`);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }
    
    // Read the PDF file
    const dataBuffer = fs.readFileSync(filePath);
    const invoiceId = path.basename(filename, '.pdf');
    
    // Extract text from PDF
    const pdfData = await pdfParse(dataBuffer);
    const pdfText = pdfData.text;
    
    // Parse the invoice text
    const invoiceData = parseInvoiceText(pdfText);
    
    if (!invoiceData.valid) {
      console.error('Invoice validation failed - totals do not match');
    }
    
    // Save to repository
    const repository = directoryManager.getInvoiceRepository();
    await repository.saveInvoice(invoiceId, invoiceData);
    
    // Generate specialized summaries
    const summaries = await generateAccountSummaries(invoiceData);
    
    // Save summaries to JSON files
    const conversationSummaryPath = path.join(outputDir, `${invoiceId}_conversations.json`);
    const financialSummaryPath = path.join(outputDir, `${invoiceId}_financial.json`);
    
    fs.writeFileSync(
      conversationSummaryPath, 
      JSON.stringify(summaries.conversationSummaries, null, 2)
    );
    
    fs.writeFileSync(
      financialSummaryPath, 
      JSON.stringify(summaries.financialSummaries, null, 2)
    );
    
    // Report data to external endpoint
    const reporter = new DataReporter({
      endpoint: process.env.CENTRALIZER_ENDPOINT
    });
    const reporterSummary = await reporter.reportAllSummaries(summaries, invoiceId);
    console.log(JSON.stringify(reporterSummary, null, 2));
    
    // Copy file to processed directory then delete original
    // Using copyFile + unlink instead of rename to handle cross-device mounts
    const processedFilePath = path.join(processedDir, filename);
    fs.copyFileSync(filePath, processedFilePath);
    fs.unlinkSync(filePath);
    
    console.log(`Successfully processed invoice ${invoiceId}`);
    return true;
  } catch (error) {
    console.error(`Error processing invoice ${filename}:`, error);
    
    // Copy file to error directory then delete original
    // Using copyFile + unlink instead of rename to handle cross-device mounts
    const errorFilePath = path.join(errorDir, filename);
    if (fs.existsSync(filePath)) {
      fs.copyFileSync(filePath, errorFilePath);
      fs.unlinkSync(filePath);
    }
    
    return false;
  }
}

export default processInvoicePdf;