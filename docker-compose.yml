services:
  app:
    env_file:
      - .env
    environment:
      NODE_ENV: development
      CHOKIDAR_USEPOLLING: true
      NODE_OPTIONS: --max-old-space-size=4096
    build:
      context: ./api
      dockerfile: Dockerfile
    volumes:
      - ./api:/app
      - /app/node_modules
    ports:
      - "${NODE_LOCAL_PORT}:${NODE_DOCKER_PORT}"
    depends_on:
      - mysql
    restart: unless-stopped
  mysql:
    image: mysql:8.0
    container_name: mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
    ports:
      - "${DB_LOCAL_PORT}:${DB_DOCKER_PORT}"
    command:
      --default-authentication-plugin=caching_sha2_password
    volumes:
      - db:/var/lib/mysql
      - ./api/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
  client:
    build:
      context: ./client
      dockerfile: Dockerfile.client
    volumes:
      - ./client:/app
      - /app/node_modules
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
    restart: unless-stopped
volumes:
  db:
