import { Router, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import Client from '../database/models/client';
import dotenv from 'dotenv';
import { checkGroupMembership, verifyGoogleToken } from '../services/auth';

dotenv.config();
const router = Router();

router.post('/validate_token', async (req: Request, res: Response) => {
	const token = req.headers['authorization']?.split(' ')[1];

	if (!token) {
		return res.status(403).json({ message: 'No token provided!' });
	}

	try {
		const decoded = jwt.verify(token, process.env.JWT_SECRET);

		const { client, role } = decoded;
        let clientid = null;
		if (client) {
			const yoizenClient = await Client.findOne({
				where: { name: client },
			});

			if (!yoizenClient || !yoizenClient.active) {
				return res.status(403).json({ message: 'Invalid or expired token.' });
			}
            clientid = yoizenClient.id;
		}

		res.json({ token, client, role, clientid});
	} catch (error) {
		res.status(401).json({ message: 'Invalid or expired token.' });
	}
});

router.post('/generate_token', async (req: Request, res: Response) => {
	const { clientName } = req.body;
	try {
		// Buscar el cliente para obtener su ID
		const yoizenClient = await Client.findOne({
			where: { name: clientName, active: true },
		});

		if (!yoizenClient) {
			return res.status(404).json({ message: 'Client not found or inactive.' });
		}

		const token = jwt.sign({
			client: clientName,
			clientId: yoizenClient.id,
			role: 'client'
		}, process.env.JWT_SECRET);

		res.json({ token });
	} catch (error) {
		res.status(401).json({ message: 'Invalid token!' });
	}
});

router.post('/validate_google_auth_code', async (req: Request, res: Response) => {
	try {
		const { googleAuthCode } = req.body;

		console.log(googleAuthCode);
		const ticket = await verifyGoogleToken(googleAuthCode);
		const { name, email } = ticket.getPayload();

		if (!email || !name) {
			return res.status(401).json({ message: 'Invalid token!' });
		}

		const googleUserRole = await checkGroupMembership(email);

		const token = jwt.sign({
			client: null,
			clientId: null,
			email,
			role: googleUserRole
		}, process.env.JWT_SECRET, {
			expiresIn: '24h',
		});

		return res.json({ token, email, name, role: googleUserRole });
	} catch (error) {
		console.error(error);
		return res.status(401).json({ message: error.message });
	}
});

// Nuevo endpoint para que admin/viewer seleccionen un cliente
router.post('/select_client', async (req: Request, res: Response) => {
	try {
		const { clientId } = req.body;
		const token = req.headers['authorization']?.split(' ')[1];

		if (!token) {
			return res.status(403).json({ message: 'No token provided!' });
		}

		const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
		const { email, role } = decoded;

		// Solo admin y viewer pueden seleccionar clientes
		if (role !== 'admin' && role !== 'viewer') {
			return res.status(403).json({ message: 'Insufficient permissions to select client.' });
		}

		// Verificar que el cliente existe y está activo
		const yoizenClient = await Client.findOne({
			where: { id: clientId, active: true },
		});

		if (!yoizenClient) {
			return res.status(404).json({ message: 'Client not found or inactive.' });
		}

		// Crear nuevo token con el cliente seleccionado
		const newToken = jwt.sign({
			client: yoizenClient.name,
			clientId: yoizenClient.id,
			email,
			role
		}, process.env.JWT_SECRET, {
			expiresIn: '24h',
		});

		return res.json({
			token: newToken,
			client: yoizenClient.name,
			clientId: yoizenClient.id,
			email,
			role
		});
	} catch (error) {
		console.error(error);
		return res.status(401).json({ message: 'Invalid or expired token.' });
	}
});

// Endpoint para limpiar la selección de cliente (admin/viewer)
router.post('/clear_client_selection', async (req: Request, res: Response) => {
	try {
		const token = req.headers['authorization']?.split(' ')[1];

		if (!token) {
			return res.status(403).json({ message: 'No token provided!' });
		}

		const decoded = jwt.verify(token, process.env.JWT_SECRET) as any;
		const { email, role } = decoded;

		// Solo admin y viewer pueden limpiar la selección
		if (role !== 'admin' && role !== 'viewer') {
			return res.status(403).json({ message: 'Insufficient permissions to clear client selection.' });
		}

		// Crear nuevo token sin cliente seleccionado
		const newToken = jwt.sign({
			client: null,
			clientId: null,
			email,
			role
		}, process.env.JWT_SECRET, {
			expiresIn: '24h',
		});

		return res.json({
			token: newToken,
			client: null,
			clientId: null,
			email,
			role
		});
	} catch (error) {
		console.error(error);
		return res.status(401).json({ message: 'Invalid or expired token.' });
	}
});

export default router;
