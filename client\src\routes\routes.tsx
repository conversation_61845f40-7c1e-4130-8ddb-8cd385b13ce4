/* eslint-disable react-refresh/only-export-components */
import { lazy } from 'react'

const ClientEdit = lazy(() => import('@/pages/client-edit'))
const Dashboard = lazy(() => import('@/pages/dashboard'))
const Clients = lazy(() => import('@/pages/clients'))
const Counters = lazy(() => import('@/pages/counters'))
const CounterEdit = lazy(() => import ('@/pages/counters-edit'))

const coreRoutes = [
  {
    path: '/',
    component: Dashboard,
    title: 'Dashboard',
  },
  {
    path: '/clients/:id',
    component: ClientEdit,
    title: 'Client Edit',
  },
  {
    path: '/clients',
    component: Clients,
    title: 'Clients',
  },
  {
    path: '/counters',
    component: Counters,
    title: 'Counters',
  },
  {
   path: '/counters/:id',
   component: CounterEdit,
   title: 'Counters edit' 
  }
]

const routes = [...coreRoutes]
export default routes
