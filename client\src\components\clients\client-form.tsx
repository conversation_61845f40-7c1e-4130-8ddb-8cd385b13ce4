import { zodResolver } from '@hookform/resolvers/zod'
import { useF<PERSON>, Controller } from 'react-hook-form'
import * as z from 'zod'
import { Input } from '@/components/ui/input'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/button'
import { CheckboxCard } from '@/components/checkboxCard'
import { useClientsId } from '@/hooks/useClients'
import { useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useCounters } from '@/hooks/useCounters'
import { useModules } from '@/hooks/useModules'
import { useNavigate } from 'react-router-dom'
import { useMutation } from '@tanstack/react-query'
import { updateClients } from '@/api/clients'
import { toast } from 'sonner'
import { useQueryClient } from '@tanstack/react-query'
import { AuthContext } from '@/context/authContext'
import { useContext } from 'react'
import {
  Form,
  FormControl,
  FormItem,
  FormLabel,
  FormDescription,
} from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
interface ClientData {
  name: string
  description: string
  modules: string[]
  counters: string[]
  active: boolean
  isTestClient: boolean
}

const clientFormSchema = z.object({
  name: z.string().min(3).max(100),
  description: z
    .string()
    .optional()
    .refine(
      (val) => {
        return (
          val === undefined || (val.length >= 5 && val.length <= 160)
        )
      },
      {
        message:
          'Description must be between 5 and 160 characters long',
      }
    ),
  activeModules: z.array(z.number()),
  activeCounters: z.array(z.number()),
  active: z.boolean(),
  isTestClient: z.boolean(),
})

type ClientFormValues = z.infer<typeof clientFormSchema>

function ClientForm() {
  const { id } = useParams()
  const { data: client } = useClientsId(id)
  const { data: counters } = useCounters()
  const { data: modules } = useModules()

  const { getUserInfo } = useContext(AuthContext)

  const { role } = getUserInfo()
  const isAdmin = role === 'admin'

  const getEmptyClientsForm = (data: ClientData) => {
    return {
      name: data?.name || '',
      description: data?.description
        ? data.description.toString()
        : undefined,
      activeModules: data?.modules
        ? [...data.modules.map((x) => x.id)]
        : [],
      activeCounters: data?.counters
        ? [...data.counters.map((x) => x.id)]
        : [],
      active: !!data?.active,
      isTestClient: !!data?.isTestClient,
    }
  }

  const form = useForm<ClientFormValues>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      ...getEmptyClientsForm(client),
    },
  })

  useEffect(() => {
    form.reset({ ...getEmptyClientsForm(client) })
  }, [client])

  const { t } = useTranslation()

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = form

  function onSubmit(data: ClientFormValues) {
    mutate({ ...data, id: id })
  }

  const getCounters = () =>
    counters?.map((counter) => ({
      id: counter.id,
      label: counter.name,
    })) || []

  const getModules = () =>
    modules?.map((module) => ({
      id: module.id,
      label: module.name,
    })) || []

  const navigate = useNavigate()

  const onCancel = () => {
    navigate('/clients')
  }

  const queryClient = useQueryClient()

  const { mutate } = useMutation({
    mutationFn: updateClients,
    onSuccess: (data) => {
      if (data) {
        toast.success(t('messageCounterSave'))
        queryClient.invalidateQueries(`clients-${id}`)
        navigate('/clients')
      }
    },
  })

  return (
    <>
      <Form {...form}>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
          <div className="flex flex-col space-y-2">
            <label htmlFor="name">{t('name')}</label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  id="name"
                  disabled={!isAdmin}
                  placeholder={t('namePlaceholder')}
                  {...field}
                />
              )}
            />
            {errors.name && (
              <p className="text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="flex flex-col space-y-2">
            <label htmlFor="description">{t('description')}</label>
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Input
                  id="description"
                  disabled={!isAdmin}
                  placeholder={t('descriptionPlaceHolder')}
                  {...field}
                />
              )}
            />
            {errors.description && (
              <p className="text-red-500">
                {errors.description.message}
              </p>
            )}
          </div>

          <CheckboxCard
            control={control}
            items={getModules()}
            name="activeModules"
            title={t('activeModules')}
            disabled={!isAdmin}
            description={t('selectModules')}
            defaultValue={[...form.watch('activeModules')]}
          />

          <CheckboxCard
            control={control}
            items={getCounters()}
            name="activeCounters"
            title={t('activeCounters')}
            disabled={!isAdmin}
            description={t('selectCounters')}
            defaultValue={[...form.watch('activeCounters')]}
          />
          <Controller
            control={control}
            name="active"
            render={({ field: { value, onChange } }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('activeClientSwitcherTitle')}
                  </FormLabel>
                  <FormDescription>
                    {t('activeClientSwitcherDescription')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    disabled={!isAdmin}
                    name="active"
                    onCheckedChange={onChange}
                    checked={value}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <Controller
            control={control}
            name="isTestClient"
            render={({ field: { value, onChange } }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">
                    {t('isTestClientSwitcherTitle')}
                  </FormLabel>
                  <FormDescription>
                    {t('isTestClientSwitcherDescription')}
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    disabled={!isAdmin}
                    name="isTestClient"
                    onCheckedChange={onChange}
                    checked={value}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-5">
            <Button variant="secondary" onClick={() => onCancel()}>
              {t('cancel')}
            </Button>
            {isAdmin && <Button>{t('save')}</Button>}
          </div>
        </form>
      </Form>
    </>
  )
}

export default ClientForm
