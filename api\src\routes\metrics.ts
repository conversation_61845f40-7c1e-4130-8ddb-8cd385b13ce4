// metricsRouter.ts
import { Router, Request, Response } from 'express'
import { fetchMetrics, fetchMetricsReport } from '../services/metrics'

const router = Router()

router.get('/', async (req: Request, res: Response) => {
  try {
    const { clientIds, dateFrom, dateTo } = req.query;
    const { role, clientId } = req;

    // Si es un cliente, usamos su clientId en lugar del que viene en la consulta
    const clientIdsToUse = role === 'client' ? clientId : clientIds;

    const metrics = await fetchMetrics(
      clientIdsToUse,
      dateFrom,
      dateTo,
      role
    );
    res.json(metrics);
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() });
  }
});

router.get('/report', async (req: Request, res: Response) => {
  try {
    const { clientIds, dateFrom, dateTo } = req.query
    const { role, clientId } = req

    // Si es un cliente, usamos su clientId en lugar del que viene en la consulta
    const clientIdsToUse = role === 'client' ? clientId : clientIds;

    const report = await fetchMetricsReport(
      clientIdsToUse,
      dateFrom,
      dateTo,
      role
    )

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="report-yoizen-console.xlsx"'
    )

    res.send(report)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

export default router
