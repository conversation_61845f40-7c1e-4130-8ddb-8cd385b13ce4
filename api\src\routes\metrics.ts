// metricsRouter.ts
import { Router, Request, Response } from 'express'
import { fetchMetrics, fetchMetricsReport } from '../services/metrics'
import clientFilterMiddleware from '../middleware/clientFilterMiddleware'

const router = Router()

router.get('/', clientFilterMiddleware, async (req: Request, res: Response) => {
  try {
    const { clientIds, dateFrom, dateTo } = req.query;
    const { role } = req;

    const metrics = await fetchMetrics(
      clientIds,
      dateFrom,
      dateTo,
      role
    );
    res.json(metrics);
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() });
  }
});

router.get('/report', clientFilterMiddleware, async (req: Request, res: Response) => {
  try {
    const { clientIds, dateFrom, dateTo } = req.query
    const { role } = req

    const report = await fetchMetricsReport(
      clientIds,
      dateFrom,
      dateTo,
      role
    )

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader(
      'Content-Disposition',
      'attachment; filename="report-yoizen-console.xlsx"'
    )

    res.send(report)
  } catch (error) {
    res.status(500).json({ error: error.message || error.toString() })
  }
})

export default router
