import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'

const validateTokenMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const token = req.headers['authorization']?.split(' ')[1]

  if (!token) {
    return res.status(403).json({ message: 'No token provided!' })
  }
  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET) as any
    const { role, client, clientId } = decoded

    if (!role) {
      return res
        .status(403)
        .json({ message: 'Invalid or expired token!' })
    }

    // Para usuarios con rol 'client', el clientId debe estar presente
    if (role === 'client' && (!client || !clientId)) {
      return res
        .status(403)
        .json({ message: 'Invalid client token - missing client information!' })
    }

    req.role = role
    req.clientName = client
    req.clientId = clientId
    next()
  } catch (error) {
    res.status(400).json({ message: 'Invalid or expired token!' })
  }
}

export default validateTokenMiddleware
