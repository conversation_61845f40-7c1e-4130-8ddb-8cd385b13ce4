import { Request, Response, NextFunction } from 'express'
import jwt from 'jsonwebtoken'

const validateTokenMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const token = req.headers['authorization']?.split(' ')[1]

  if (!token) {
    return res.status(403).json({ message: 'No token provided!' })
  }
  try {
    const { role, client, clientId } = jwt.verify(token, process.env.JWT_SECRET)
    if (!role) {
      return res
        .status(403)
        .json({ message: 'Invalid or expired token!' })
    }
    req.role = role
    req.clientName = client
    req.clientId = clientId
    next()
  } catch (error) {
    res.status(400).json({ message: 'Invalid or expired token!' })
  }
}

export default validateTokenMiddleware
