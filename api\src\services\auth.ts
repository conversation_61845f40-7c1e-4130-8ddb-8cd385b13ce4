import { OAuth2Client } from 'google-auth-library'
import { google } from 'googleapis'
import path from 'path'


const checkGroupMembership = async (memberEmail) => {
  const keyFilePath = path.join(
    __dirname,
    '..',
    'utils',
    'auth',
    'google-service-account-keys.json'
  )


  const auth = new google.auth.GoogleAuth({
    keyFile: keyFilePath,
    scopes: [
      'https://www.googleapis.com/auth/admin.directory.user',
      'https://www.googleapis.com/auth/admin.directory.group',
    ],
    clientOptions: {
      subject: '<EMAIL>',
    },
  })
  const service = google.admin({
    version: 'directory_v1',
    auth,
  })
  const adminGroup = process.env.GOOGLE_ADMIN_GROUP;
  const viewerGroup = process.env.GOOGLE_VIEWER_GROUP;

  const adminGroupResponse = await service.members.hasMember({
    groupKey: adminGroup,
    memberKey: memberEmail,
  })

  if (adminGroupResponse.data.isMember) {
    return 'admin'
  } else {
    const viewerGroupResponse = await service.members.hasMember({
      groupKey: viewerGroup,
      memberKey: memberEmail,
    })

    if (viewerGroupResponse.data.isMember) {
      return 'viewer'
    }
  }

  throw new Error('User is not a member of any group')
}

const verifyGoogleToken = async (googleAuthCode: string) => {
  const client = new OAuth2Client(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    process.env.GOOGLE_CLIENT_REDIRECT_URL
  )

  const authResponse = await client.getToken(googleAuthCode)

  const { id_token } = authResponse.tokens

  const ticket = await client.verifyIdToken({
    idToken: id_token,
    audience: process.env.GOOGLE_CLIENT_ID,
  })

  return ticket
}

export { checkGroupMembership, verifyGoogleToken }
