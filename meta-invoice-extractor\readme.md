# Extractor de Facturas Meta

Este servicio extrae datos de facturas PDF de Meta/WhatsApp, procesa la información y la almacena en un formato estructurado.

## Características

- Extrae detalles de facturas desde archivos PDF
- Analiza información de cuentas y datos de conversaciones
- Almacena datos estructurados en base de datos MySQL
- Genera resúmenes especializados:
  - Resúmenes de tipos de conversación (por cuenta)
  - Resúmenes financieros con tarifas de Yoizen
- Reporta datos a endpoints externos

## Configuración de Entorno

### Variables de Entorno

Crea un archivo `.env` con las siguientes variables:

```
# Puerto del servidor
PORT=3000

# Directorios de trabajo
INPUT_DIR=/input
OUTPUT_DIR=/output
PROCESSED_DIR=/processed
ERROR_DIR=/error

# Base de datos MySQL para datos de facturas
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=password
MYSQL_DATABASE=whatsapp_dev

# Base de datos MySQL para tarifas de Yoizen (opcional)
WHATSAPP_DB_HOST=localhost
WHATSAPP_DB_USER=root
WHATSAPP_DB_PASSWORD=password
WHATSAPP_DB_NAME=whatsapp

# Endpoint para reportes
CENTRALIZER_ENDPOINT=http://tu-api-endpoint.com/api

# Activar repositorios adicionales
USE_ADDITIONAL_REPOSITORIES=true
```

### Instalación Local

1. Instalar dependencias:
   ```
   npm install
   ```

2. Inicializar el esquema de la base de datos:
   ```
   mysql -u root -p < schema.sql
   ```

## Uso del Servicio

### Modo Desarrollo

Para ejecutar el servicio en modo desarrollo:

```bash
# Ejecutar directamente con Node.js
npm run dev

# O con Docker en modo desarrollo
docker build -t meta-invoice-extractor:dev -f Dockerfile.development .
docker run -p 3000:3000 -p 9229:9229 --env-file .env \
    -v "$(pwd)/src:/app/src" \
    -v "$(pwd)/../input:/input" \
    -v "$(pwd)/../output:/output" \
    -v "$(pwd)/../processed:/processed" \
    -v "$(pwd)/../error:/error" \
    meta-invoice-extractor:dev
```

### Modo Producción

Para ejecutar el servicio en modo producción:

```bash
# Construir la imagen de producción
docker build -t meta-invoice-extractor:latest -f Dockerfile .

# Ejecutar el contenedor
docker run -p 3000:3000 --env-file .env \
    -v "$(pwd)/../input:/input" \
    -v "$(pwd)/../output:/output" \
    -v "$(pwd)/../processed:/processed" \
    -v "$(pwd)/../error:/error" \
    meta-invoice-extractor:latest
```

## API Endpoints

- `POST /api/upload` - Subir un archivo PDF para procesamiento. El archivo se colocará en el directorio de entrada y será procesado automáticamente por el watcher.

Ejemplo de uso:
```bash
curl -X POST \
  -F "file=@./Transacción_5100018192.pdf" \
  http://localhost:3000/api/upload
```

## Cómo Funciona

1. Cuando se sube un archivo PDF a través de la API, se coloca en el directorio de entrada (`/input`)
2. El sistema detecta automáticamente el archivo y comienza a procesarlo
3. Se extrae el texto del PDF y se procesa para obtener detalles de la factura, cuentas y elementos de conversación
4. Los datos se guardan en la base de datos MySQL (si está configurada)
5. Se generan resúmenes especializados en el directorio de salida (`/output`):
   - Resúmenes de cuentas con totales por tipo de conversación
   - Resúmenes financieros con tarifas de Yoizen
6. El archivo PDF procesado se mueve al directorio de procesados (`/processed`)
7. En caso de error, el archivo se mueve al directorio de errores (`/error`)

## Estructura de Directorios

- `/input` - Directorio donde se colocan los PDFs a procesar
- `/output` - Directorio donde se generan los archivos JSON de salida
- `/processed` - Directorio donde se mueven los PDFs procesados exitosamente
- `/error` - Directorio donde se mueven los PDFs que tuvieron errores al procesarse

## Despliegue en Kubernetes

### Configuración Básica

El servicio puede desplegarse en Kubernetes utilizando los archivos de configuración en el directorio `k8s/`:

```bash
# Aplicar configuraciones
kubectl apply -f k8s/configmap.yaml
kubectl apply -f k8s/deployment.yaml
kubectl apply -f k8s/ingress.yaml
```

### Montaje de Azure Blob Storage

Para entornos de producción, es recomendable utilizar Azure Blob Storage en lugar de volúmenes locales para persistir los archivos:

1. **Configuración del Secret para Azure Storage**

   Ejecuta el script de creación de secretos:
   ```bash
   ./k8s/create-secrets.sh
   ```
   
   O crea el secreto manualmente:
   ```bash
   kubectl create secret generic azure-storage-secret \
     --from-literal=azurestorageaccountname=<nombre-cuenta> \
     --from-literal=azurestorageaccountkey=<clave-cuenta>
   ```

2. **Aplicar la configuración de Storage**

   ```bash
   kubectl apply -f k8s/azure-storage.yaml
   ```

3. **Configuración en el Deployment**

   El archivo `k8s/deployment.yaml` ya está configurado para montar los volúmenes de Azure Blob Storage:

   ```yaml
   volumes:
     - name: input-volume
       persistentVolumeClaim:
         claimName: azure-input-pvc
     - name: output-volume
       persistentVolumeClaim:
         claimName: azure-output-pvc
     - name: processed-volume
       persistentVolumeClaim:
         claimName: azure-processed-pvc
     - name: error-volume
       persistentVolumeClaim:
         claimName: azure-error-pvc
   ```

### Variables de Entorno en Kubernetes

Las variables de entorno se configuran a través de un ConfigMap:

```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: meta-invoice-extractor-config
data:
  PORT: "3000"
  INPUT_DIR: "/input"
  OUTPUT_DIR: "/output"
  PROCESSED_DIR: "/processed"
  ERROR_DIR: "/error"
  MYSQL_HOST: "mysql-service"
  MYSQL_DATABASE: "whatsapp_dev"
  NODE_ENV: "production"
  USE_ADDITIONAL_REPOSITORIES: "true"
```

Los secretos como contraseñas deben configurarse en un Secret separado:

```bash
kubectl create secret generic db-credentials \
  --from-literal=MYSQL_USER=usuario \
  --from-literal=MYSQL_PASSWORD=contraseña
```

## Solución de Problemas

### Errores comunes

- **Error EACCES al iniciar**: Asegúrate de que los directorios configurados en las variables de entorno existan y tengan permisos correctos
- **Error EXDEV al mover archivos**: Este error ocurre cuando se intenta mover archivos entre diferentes sistemas de archivos o volúmenes de Docker. El sistema maneja esto copiando los archivos en lugar de moverlos
- **Problemas con Azure Blob Storage**: Verifica que las credenciales son correctas y que los contenedores existen en tu cuenta de almacenamiento

### Validación de Facturas

El sistema valida los totales extraídos de la factura. Si los totales calculados no coinciden con los totales esperados en el resumen de la factura, se mostrará un mensaje de advertencia, pero el procesamiento continuará.
