import apiInstance from './api'
import ApiResponse from '@/types/apiResponse'
import Client from '@/types/client'

export interface ClientsInterface {
  [key: string]: any
}

export const fetchClients = async (
  filterTestClients: boolean = false
): Promise<ApiResponse<Client[]>> => {
  try {
    const response = await apiInstance.get(
      `/clients?filterTestClients=${filterTestClients}`
    )
    return response.data
  } catch (error) {
    console.error('Error fetching clients', error)
    throw error
  }
}

export const fetchClientsId = async (
  id: string
): Promise<ApiResponse<Client>> => {
  try {
    const response = await apiInstance.get(`/clients/${id}`)
    return response.data
  } catch (error) {
    console.error('Error fetching client', error)
    throw error
  }
}

export const updateClients = async (
  client: ClientsInterface
): Promise<ApiResponse<Client>> => {
  try {
    const response = await apiInstance.put(
      `/clients/${client.id}`,
      client
    )
    return response.data
  } catch (error) {
    console.error('Error updating client', error)
    throw error
  }
}
