import Sequelize from 'sequelize'
import { parseISO, format, isAfter, isEqual } from 'date-fns'
import ExcelJS from 'exceljs'
import Client from '../database/models/client'
import i18next from 'i18next'
import { setMetricsCellStyles } from '../utils/excel'
import Counter from '../database/models/counter'
import ClientMetrics from '../database/models/client-metrics'
import CounterGroup from '../database/models/counter-group'

const servicesCounters = [
  'new_cases',
  'new_messages',
  'messages_payment',
  'replied_messages',
  'outbound_messages',
  'outgoing',
  'yflow_replied_messages',
  'closed_cases_only_with_yflow',
  'max_concurrent_agents',
]

const fetchCountersForClient = async (clientIds, role) => {
  if (role === 'client') {
    return await Counter.scope('withAssociations').findAll({
      include: [
        {
          model: Client,
          where: { id: clientIds },
          through: { attributes: [] }
        }
      ]
    })
  } else {
    return await Counter.scope('withAssociations').findAll()
  }
}

const isValidDate = (dateString) => {
  if (typeof dateString !== 'string') {
    return false
  }

  const parsedDate = parseISO(dateString)
  return !isNaN(parsedDate.getTime())
}
const fetchDailyMetricsGrouped = async (clientIds, dateFrom, dateTo, role) => {
  const counters = await fetchCountersForClient(clientIds, role);
  const counterCodes = counters
    .filter(counter => counter.active == true && counter.unit !== null)
    .map((counter) => counter.code);

  const metrics = await ClientMetrics.findAll({
    where: {
      client_id: clientIds,
      date_from: {
        [Sequelize.Op.gte]: dateFrom,
        [Sequelize.Op.lte]: dateTo,
      },
      metric_name: { [Sequelize.Op.in]: counterCodes },
    },
    attributes: ["metricName", "reference", "metricValue", "dateFrom"],
    group: ["metric_name", "date_from", "reference", "metric_value"],
    raw: true,
  });

  const parsedMetrics = metrics.map((metric) => {
    const counter = counters.find((c) => c.code === metric.metricName);
    return {
      ...metric,
      code: counter?.code,
      name: counter?.name,
      counter_type: counter?.type?.name,
      unit_type: counter?.unit?.name,
      metric_date: metric.dateFrom,
      reference: metric.reference,
      metric_value: metric.metricValue,
    };
  });

  return parsedMetrics;
};

const fetchDailyMetricsDetailed  = async (clientIds, dateFrom, dateTo, role) => {
  const counters = await fetchCountersForClient(clientIds, role);
  const counterCodes = counters
    .filter(counter => counter.active == true && counter.unit !== null)
    .map((counter) => counter.code);

  const metrics = await ClientMetrics.findAll({
    where: {
      client_id: clientIds,
      date_from: {
        [Sequelize.Op.gte]: dateFrom,
        [Sequelize.Op.lte]: dateTo,
      },
      metric_name: { [Sequelize.Op.in]: counterCodes },
    },
    attributes: ["metricName", "reference", "metricValue", "dateFrom", "clientId"],
    group: ["metric_name", "date_from", "reference", "metric_value", "client_id"],
    raw: true,
  });

  const parsedMetrics = metrics.map((metric) => {
    const counter = counters.find((c) => c.code === metric.metricName);
    return {
      ...metric,
      code: counter?.code,
      name: counter?.name,
      counter_type: counter?.type?.name,
      unit_type: counter?.unit?.name,
      metric_date: metric.dateFrom,
      reference: metric.reference,
      metric_value: metric.metricValue,
    };
  });

  return parsedMetrics;
};

const fetchMetricsForMultipleClients = async (
  clientIds,
  dateFrom,
  dateTo,
  role
) => {
  const dailyMetrics = await fetchDailyMetricsDetailed (
    clientIds,
    dateFrom,
    dateTo,
    role
  );

  const metricsPerClient = {};

  // Inicializar estructura para cada cliente
  clientIds.forEach(clientId => {
    metricsPerClient[clientId.toString()] = {};
  });

  dailyMetrics.forEach((metric) => {
    const {
      code,
      metric_value,
      counter_type,
      metric_date,
      unit_type,
      name,
      reference,
      clientId
    } = metric;

    const clientIdStr = clientId.toString();

    if (!metricsPerClient[clientIdStr][code]) {
      metricsPerClient[clientIdStr][code] = {
        totalSum: 0,
        maxSum: 0,
        maxDay: null,
        counter_type,
        name,
        unit_type,
        reference,
      };
    }

    if (reference) {
      metricsPerClient[clientIdStr][code].reference = reference;
    }

    if (counter_type === "max") {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      if (value > metricsPerClient[clientIdStr][code].maxSum) {
        metricsPerClient[clientIdStr][code].maxSum = value;
        metricsPerClient[clientIdStr][code].maxDay = metric_date;
      }
    }

    if (counter_type === "count") {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      metricsPerClient[clientIdStr][code].totalSum += value;
    }

    if (counter_type === "snapshot" && metric_value) {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      if (
        isAfter(metric_date, metricsPerClient[clientIdStr][code].maxDay) ||
        !metricsPerClient[clientIdStr][code].maxDay
      ) {
        metricsPerClient[clientIdStr][code].maxSum = value;
        metricsPerClient[clientIdStr][code].maxDay = metric_date;
      } else if (isEqual(metric_date, metricsPerClient[clientIdStr][code].maxDay)) {
        metricsPerClient[clientIdStr][code].maxSum += value;
        metricsPerClient[clientIdStr][code].maxDay = metric_date;
      }
    }
  });

  // Procesar el resultado final para cada cliente
  const finalResult = {};
  Object.entries(metricsPerClient).forEach(([clientId, metrics]) => {
    finalResult[clientId] = {};

    Object.keys(metrics).forEach((code) => {
      const metric = metrics[code];
      if (metric.counter_type === "count") {
        finalResult[clientId][code] = { totalSum: metric.totalSum };
      } else {
        finalResult[clientId][code] = {
          maxSum: metric.maxSum,
          maxDay: metric.maxDay,
        };
      }
      finalResult[clientId][code] = {
        ...finalResult[clientId][code],
        unitType: metric.unit_type,
        name: metric.name,
        reference: metric.reference,
      };
    });
  });

  return Object.keys(finalResult).length > 0 ? finalResult : null;
};

// Función para obtener métricas agrupadas (reporte visual)
export const fetchMetricsGrouped = async (clientIds, dateFrom, dateTo, role) => {
  const clientIdArray = Array.isArray(clientIds)
    ? clientIds
    : clientIds
        .split(",")
        .map((id) => parseInt(id.trim(), 10))
        .filter((id) => !isNaN(id));

  const sanitizedDateFrom = isValidDate(dateFrom)
    ? format(parseISO(dateFrom), "yyyy-MM-dd")
    : null;
  const sanitizedDateTo = isValidDate(dateTo)
    ? format(parseISO(dateTo), "yyyy-MM-dd")
    : null;

  if (!sanitizedDateFrom || !sanitizedDateTo) {
    throw new Error("Invalid date format");
  }

  const dailyMetrics = await fetchDailyMetricsGrouped(
    clientIdArray,
    sanitizedDateFrom,
    sanitizedDateTo,
    role
  );

  // Agrupar métricas por código - combinando datos de todos los clientes
  const groupedMetrics = {};

  dailyMetrics.forEach((metric) => {
    const {
      code,
      metric_value,
      counter_type,
      metric_date,
      unit_type,
      name,
      reference
    } = metric;

    if (!groupedMetrics[code]) {
      groupedMetrics[code] = {
        totalSum: 0,
        maxSum: 0,
        maxDay: null,
        counter_type,
        name,
        unit_type,
        reference
      };
    }

    if (counter_type === "max") {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      if (value > groupedMetrics[code].maxSum) {
        groupedMetrics[code].maxSum = value;
        groupedMetrics[code].maxDay = metric_date;
      }
    }

    if (counter_type === "count") {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      groupedMetrics[code].totalSum += value;
    }

    if (counter_type === "snapshot" && metric_value) {
      const value = typeof metric_value === 'string' ? parseFloat(metric_value) : metric_value;
      if (
        isAfter(metric_date, groupedMetrics[code].maxDay) ||
        !groupedMetrics[code].maxDay
      ) {
        groupedMetrics[code].maxSum = value;
        groupedMetrics[code].maxDay = metric_date;
      } else if (isEqual(metric_date, groupedMetrics[code].maxDay)) {
        groupedMetrics[code].maxSum += value;
        groupedMetrics[code].maxDay = metric_date;
      }
    }
  });

  return groupedMetrics;
};

// Función para obtener métricas sin agrupar (para Excel)
export const fetchMetricsDetailed = async (clientIds, dateFrom, dateTo, role) => {
  const clientIdArray = Array.isArray(clientIds)
    ? clientIds
    : clientIds
        .split(",")
        .map((id) => parseInt(id.trim(), 10))
        .filter((id) => !isNaN(id));

  const sanitizedDateFrom = isValidDate(dateFrom)
    ? format(parseISO(dateFrom), "yyyy-MM-dd")
    : null;
  const sanitizedDateTo = isValidDate(dateTo)
    ? format(parseISO(dateTo), "yyyy-MM-dd")
    : null;

  if (!sanitizedDateFrom || !sanitizedDateTo) {
    throw new Error("Invalid date format");
  }

  return await fetchMetricsForMultipleClients(
    clientIdArray,
    sanitizedDateFrom,
    sanitizedDateTo,
    role
  );
};

export const fetchMetrics = async (clientIds, dateFrom, dateTo, role) => {
  return await fetchMetricsGrouped(clientIds, dateFrom, dateTo, role);
};

export const fetchMetricsReport = async (
  clientIds,
  dateFrom,
  dateTo,
  role
) => {
  const metrics = await fetchMetricsDetailed(clientIds, dateFrom, dateTo, role);
  const buffer = await createExcelFromMetrics(metrics, clientIds, dateFrom, dateTo);
  return buffer;
}

export const createExcelFromMetrics = async (
  metrics,
  clientIds,
  dateFrom,
  dateTo
) => {
  const workbook = new ExcelJS.Workbook()
  const worksheet = workbook.addWorksheet('Metrics')

  try {
    const clientIdArray = Array.isArray(clientIds)
      ? clientIds
      : clientIds
          .split(',')
          .map((id) => parseInt(id.trim(), 10))
          .filter((id) => !isNaN(id))

    const clients = await Promise.all(
      clientIdArray.map((id) => Client.findByPk(id))
    )

    worksheet.addRow([`${i18next.t('period')}`, dateFrom, dateTo])
    worksheet.addRow([])

    if (!metrics) {
      worksheet.addRow([i18next.t('no_data_available')])
      const buffer = await workbook.xlsx.writeBuffer()
      return buffer
    }

    const firstClientMetrics = Object.values(metrics)[0] || {}
    const allMetricCodes = Object.keys(firstClientMetrics)

    const headers = ['Cliente']
    allMetricCodes.forEach(code => {
      const metricData = firstClientMetrics[code]
      const metricName = metricData?.name || code
      headers.push(`${metricName} ${i18next.t('value')}`)
      headers.push(`${metricName} ${i18next.t('reference')}`)
    })
    worksheet.addRow(headers)


    for (const client of clients) {
      if (!client) continue

      const clientMetrics = metrics[client.id.toString()] || {}
      const rowData = [client.dataValues.name]

      allMetricCodes.forEach(code => {
        const metricData = clientMetrics[code]
        rowData.push(metricData?.totalSum || metricData?.maxSum || 0)
        rowData.push(metricData?.reference || '')
      })

      worksheet.addRow(rowData)
    }

    setMetricsCellStyles(worksheet)

    const buffer = await workbook.xlsx.writeBuffer()
    return buffer
  } catch (error) {
    console.error('Error processing Excel file:', error)
    throw error
  }
}

const metricExists = async (
  clientId,
  date,
  moduleId,
  metricName,
  transaction
) => {
  if (metricName !==  undefined){
    const metric = await ClientMetrics.findOne({
      where: {
        clientId,
        dateFrom: parseISO(date),
        moduleId,
        metricName,
      },
      attributes: ['clientId', 'moduleId', 'dateFrom', 'metricName'],
      transaction,
    })
    return Boolean(metric)
  }
  console.log(`metricExists:  metricName es undefined `);

  return Boolean(null)
}

const createMetric = async (
  clientId,
  date,
  moduleId,
  metricName,
  metricValue,
  reference,
  transaction
) => {
  const exists = await metricExists(
    clientId,
    date,
    moduleId,
    metricName,
    transaction
  )

  if (exists) {
    console.log(
      `Metric ${metricName} for client ${clientId} and date ${date} already exists`
    )
  } else if (metricName !== undefined) {
        await ClientMetrics.create(
          {
            clientId,
            moduleId,
            dateFrom: format(parseISO(date), 'yyyy-MM-dd'),
            metricName,
            metricValue,
            reference,
          },
          { transaction }
        )
  }
}


const createNewCounter = async (code, transaction) => {
  console.log(`createNewCounter Name: ${code}`);
   const group = await CounterGroup.findOne({
    where: { name: 'Incompletos' },
    attributes: ['id'],
  })

  if (!group) {
    throw new Error('Group "Incompletos" not found');
  }

  await Counter.create(
    {
      code,
      name: `[Incomplete]${code}`,
      description: `[Incomplete]${code}`,
      active: false,
      type: null,
      unit: null,
      groupId: group.id,
    },
    { transaction }
  )
  return code
}

const processCounters = async (clientId, date, moduleId, counters, transaction) => {
  for (const counter of counters) {
    if (counter.name !== undefined)
    {
      let existingCounter = await Counter.findOne({ where: { code: counter.name } });

      if (!existingCounter) {
        existingCounter = await createNewCounter(counter.name, transaction);
      }

      await createMetric(
        clientId,
        date,
        moduleId,
        counter.name,
        counter.value,
        counter.reference,
        transaction
      );
    }
  }
};

const processServices = async (clientId, date, moduleId, services, transaction) => {
  const groupedMetrics = {};
  for (const service of services) {
    for (const serviceProp in service) {
      const metricValue = service[serviceProp];

      if (metricValue !== undefined && servicesCounters.includes(serviceProp)) {
        if (groupedMetrics[serviceProp]) {
          groupedMetrics[serviceProp] += metricValue;
        } else {
          groupedMetrics[serviceProp] = metricValue;
        }
      }
    }
  }

  for (const [metricName, metricValue] of Object.entries(groupedMetrics)) {
    await createMetric(
      clientId,
      date,
      moduleId,
      metricName,
      metricValue,
      undefined,
      transaction
    );
  }
};

const processMetrics = async (clientId, date, moduleId, metrics, transaction) => {
  const counters = await Counter.findAll();
  for (const counter of counters) {
    console.log(`${counter.code}: ${metrics[counter.code]}`);

    const metricValue = metrics[counter.code];
    if (metricValue !== undefined) {
      await createMetric(
        clientId,
        date,
        moduleId,
        counter.code,
        metricValue,
        undefined,
        transaction
      );
    }
  }

  for (const metricName in metrics) {
    if (
      metrics[metricName] !== undefined && !servicesCounters.includes(metricName) &&
      !counters.some((counter) => counter.code === metricName)
    ) {
      const newCounter = await createNewCounter(metricName, transaction);
      if (metrics[metricName] !== undefined ){
        await createMetric(
          clientId,
          date,
          moduleId,
          metricName,
          metrics[metricName],
          undefined,
          transaction
        );
      }

    }
  }
};

export const createMetrics = async (data, transaction) => {
  const { clientId, date, moduleId, metrics } = data;

  if (metrics.counters) {
    await processCounters(clientId, date, moduleId, metrics.counters, transaction);
  }

  if (metrics.services) {
    await processServices(clientId, date, moduleId, metrics.services, transaction);
  }
  // Deprecado, cambiamos en Social para que venga dentro de Counters directamente
  //await processMetrics(clientId, date, moduleId, metrics, transaction);
};
