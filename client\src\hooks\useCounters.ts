import {
  fetchCounters,
  fetchCounterGroups,
  fetchCounterTypes,
  fetchCounter,
  fetchCounterUnits,
} from '@/api/counters'
import { useQuery } from '@tanstack/react-query'
import { DEFAULT_CACHE_TIME } from '@/lib/constants'
import { Counter } from '@/models/counter'

const useCounters = () => {
  return useQuery({
    queryKey: ['counters'],
    queryFn: fetchCounters,
    staleTime: DEFAULT_CACHE_TIME,
  })
}

const useCounter = (id: string | undefined) => {
  return useQuery<Counter>({
    queryKey: [`counterId-${id}`],
    queryFn: () => fetchCounter(id),
    staleTime: DEFAULT_CACHE_TIME,
  })
}

const useCounterGroups = () => {
  return useQuery({
    queryKey: ['counterGroups'],
    queryFn: fetchCounterGroups,
    staleTime: DEFAULT_CACHE_TIME,
  })
}

const useCounterTypes = () => {
  return useQuery({
    queryKey: ['counterTypes'],
    queryFn: fetchCounterTypes,
    staleTime: DEFAULT_CACHE_TIME,
  })
}

const useCounterUnits = () => {
  return useQuery({
    queryKey: ['counterUnits'],
    queryFn: fetchCounterUnits,
    staleTime: DEFAULT_CACHE_TIME,
  })
}

export {
  useCounters,
  useCounterGroups,
  useCounterTypes,
  useCounter,
  useCounterUnits,
}
