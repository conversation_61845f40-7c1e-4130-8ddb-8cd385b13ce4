{"name": "yoizen-console-api", "version": "1.0.0", "description": "Yoizen console reports API", "main": "dist/app.js", "scripts": {"serve": "node dist/app.js", "dev": "nodemon --exec \"node -r dotenv/config --require dotenv/config -r ts-node/register src/app.ts dotenv_config_path=.env.development\"", "lint": "eslint . --ext .ts", "test": "echo \"Error: no test specified\" && exit 1", "seed": "docker-compose --env-file .env.development run app sh -c './run-seed-dev.sh'", "seed-production": "docker-compose --env-file .env.production run app sh -c 'npm install -g sequelize-cli && sequelize db:seed:all --config ./dist/database/config.js --seeders-path ./dist/database/seeders'", "build": "npx tsc -p ./tsconfig.json && copyfiles -u 3 src/utils/auth/* dist/utils/auth && copyfiles -u 2 src/languages/* dist/languages", "build-dev": "docker-compose --env-file .env.development -f ../docker-compose.yml -f ../docker-compose.yml up --build", "build-prod": "docker-compose --env-file .env.production -f ../docker-compose.prod.yml up --build --detach", "start-dev": "docker-compose --env-file .env.development -f ../docker-compose.yml -f ../docker-compose.yml up", "migrate-prod": "docker-compose --env-file .env.production run app sh -c 'npm install -g sequelize-cli && sequelize db:migrate --config ./dist/database/config.js --migrations-path ./dist/database/migrations'"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@azure/service-bus": "^7.9.5", "cors": "^2.8.5", "date-fns": "^3.3.1", "dotenv": "^16.3.1", "exceljs": "^4.4.0", "express": "^4.18.2", "google-auth-library": "^9.6.3", "googleapis": "^133.0.0", "i18next": "^23.8.2", "i18next-fs-backend": "^2.3.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.6.5", "sequelize": "^6.35.2", "sequelize-cli": "^6.6.2"}, "devDependencies": {"@faker-js/faker": "^8.3.1", "@types/express": "^4.17.21", "@types/i18next-fs-backend": "^1.1.5", "@types/node": "^20.10.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "copyfiles": "^2.4.1", "eslint": "^8.56.0", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}