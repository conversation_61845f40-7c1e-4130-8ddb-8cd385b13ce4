import * as React from 'react';
import { CheckIcon, PlusCircledIcon, UserIcon } from '@radix-ui/react-icons';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useTranslation } from 'react-i18next';
import { useClients } from '@/hooks/useClients';
import { useContext } from 'react';
import { AuthContext } from '@/context/authContext';
import { toast } from 'sonner';
import Client from '@/types/client';

interface ClientSelectorProps {
  onClientSelected?: (client: Client | null) => void;
}

export function ClientSelector({ onClientSelected }: ClientSelectorProps) {
  const { t } = useTranslation();
  const { data: clientsData, isLoading } = useClients(true, ['clients-no-test']);
  const { getUserInfo, selectClient, canSelectClient } = useContext(AuthContext);
  const [open, setOpen] = React.useState(false);
  const [isSelecting, setIsSelecting] = React.useState(false);

  // Parsear los datos de clientes para obtener el array correcto
  const clients = clientsData ? (Array.isArray(clientsData) ? clientsData : []) : [];

  const userInfo = getUserInfo();
  const selectedClient = clients?.find((client: Client) =>
    client.id === parseInt(userInfo.clientId || '0')
  );

  // Solo mostrar el selector si el usuario puede seleccionar clientes
  if (!canSelectClient()) {
    return null;
  }

  const handleSelectClient = async (client: Client | null) => {
    if (isSelecting) return;

    try {
      setIsSelecting(true);

      if (client) {
        await selectClient(client.id);
        toast.success(t('clientSelectedSuccessfully', { clientName: client.name }));
      }

      onClientSelected?.(client);
      setOpen(false);
    } catch (error: any) {
      console.error('Error selecting client:', error);
      toast.error(t('errorSelectingClient') + ': ' + (error.response?.data?.message || error.message));
    } finally {
      setIsSelecting(false);
    }
  };

  const handleClearSelection = async () => {
    // Para limpiar la selección, necesitaríamos un endpoint específico
    // Por ahora, simplemente cerramos el popover
    setOpen(false);
    onClientSelected?.(null);
  };

  if (isLoading) {
    return (
      <Button variant="outline" size="sm" className="h-10 border-dashed p-4" disabled>
        <UserIcon className="mr-2 h-4 w-4" />
        {t('loading')}...
      </Button>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-10 border-dashed p-4 justify-start md:justify-center"
          disabled={isSelecting}
        >
          <UserIcon className="mr-2 h-4 w-4" />
          {selectedClient ? selectedClient.name : t('selectClient')}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <Command>
          <CommandInput placeholder={t('searchClients')} />
          <CommandList>
            <CommandEmpty>{t('noClientsFound')}</CommandEmpty>
            <CommandGroup>
              {/* Opción para limpiar selección */}
              {selectedClient && (
                <CommandItem
                  onSelect={handleClearSelection}
                  className="text-muted-foreground"
                >
                  <div className="flex items-center justify-between w-full">
                    <span>{t('clearSelection')}</span>
                  </div>
                </CommandItem>
              )}

              {clients?.map((client: Client) => (
                <CommandItem
                  key={client.id}
                  onSelect={() => handleSelectClient(client)}
                  disabled={isSelecting}
                  className={`cursor-pointer ${
                    selectedClient?.id === client.id ? 'bg-accent' : ''
                  }`}
                >
                  <div className="flex items-center justify-between w-full">
                    <div className="flex flex-col">
                      <span className="font-medium">{client.name}</span>
                      {client.description && (
                        <span className="text-sm text-muted-foreground">
                          {client.description}
                        </span>
                      )}
                    </div>
                    {selectedClient?.id === client.id && (
                      <CheckIcon className="h-4 w-4" />
                    )}
                  </div>
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
