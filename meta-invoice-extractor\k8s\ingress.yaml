apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: meta-invoice-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/use-regex: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - invoice-extractor.yourdomain.com
    secretName: invoice-extractor-tls
  rules:
  - host: invoice-extractor.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: meta-invoice-extractor
            port:
              number: 80