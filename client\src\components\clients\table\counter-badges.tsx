import * as React from 'react'
import { Badge } from '@/components/ui/badge'
import Counter from '@/types/counter'
import {
  <PERSON><PERSON><PERSON>,
  TooltipTrigger,
  TooltipContent,
  TooltipProvider,
} from '@/components/ui/tooltip'

type CounterBadgesProps = {
  counters: Counter[]
}

const CounterBadges = ({ counters }: CounterBadgesProps) => (
  <TooltipProvider>
    {counters.map((counter) => {
      const isLongName = counter.name.length > 12
      const displayText = isLongName
        ? `${counter.name.substring(0, 9)}...`
        : counter.name

      return (
        <React.Fragment key={counter.id}>
          {isLongName ? (
            <Tooltip delayDuration={0}>
              <TooltipTrigger asChild>
                <div>
                  <Badge className="cursor-pointer" variant="default">
                    {displayText}
                  </Badge>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                {counter.name}
              </TooltipContent>
            </Tooltip>
          ) : (
            <Badge variant="default">{counter.name}</Badge>
          )}
        </React.Fragment>
      )
    })}
  </TooltipProvider>
)

export default CounterBadges
