import { ColumnDef } from '@tanstack/react-table'
import { Badge } from '@/components/ui/badge'
import { Client } from './data/schema'
import { DataTableColumnHeader } from './column-header'
import { useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { withTranslation } from 'react-i18next'
import Counter from '@/types/counter'

export const columns: ColumnDef<Client>[] = [
  {
    accessorKey: 'name',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title={t('name')} />
    )),
    cell: ({ row }) => (
      <div className="w-[150px] truncate">
        <span className="font-medium">{row.getValue('name')}</span>
      </div>
    ),
    size: 150,
  },
  {
    accessorKey: 'description',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title={t('description')} />
    )),
    cell: ({ row }) => (
      <div className="w-[200px] truncate">
        <span>{row.getValue('description')}</span>
      </div>
    ),
    size: 200,
  },
  {
    accessorKey: 'active',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title={t('status')} />
    )),
    cell: ({ row }) => {
      const isActive = row.getValue('active')
      return (
        <div className="w-[100px]">
          <Badge variant={isActive ? 'success' : 'warning'}>
            {isActive ? 'Active' : 'Inactive'}
          </Badge>
        </div>
      )
    },
    size: 100,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: 'isTestClient',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title="Test" />
    )),
    cell: ({ row }) => (
      <div className="w-[80px]">
        {row.getValue('isTestClient') && <Badge>Test</Badge>}
      </div>
    ),
    size: 80,
  },
  {
    accessorKey: 'modules',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title={t('modules')} />
    )),
    cell: ({ row }) => {
      const modules = row.getValue('modules') as Array<{id: number, name: string}>
      const visibleModules = modules.slice(0, 2)
      const remaining = modules.length - 2

      return (
        <div className="flex flex-wrap gap-1 w-[200px]">
          {visibleModules.map(module => (
            <Badge key={module.id} variant="outline">
              {module.name}
            </Badge>
          ))}
          {remaining > 0 && (
            <Badge variant="secondary">+{remaining}</Badge>
          )}
        </div>
      )
    },
    size: 200,
  },
  {
    accessorKey: 'counters',
    header: withTranslation()(({ column, t }) => (
      <DataTableColumnHeader column={column} title={t('countersBar')} />
    )),
    cell: ({ row }) => {
      const counters = row.getValue('counters') as Counter[]
      const visibleCounters = counters.slice(0, 3)
      const remaining = counters.length - 3

      return (
        <div className="flex flex-wrap gap-1 w-[350px]">
          {visibleCounters.map(counter => (
            <Badge key={counter.id}>
              {counter.name}
            </Badge>
          ))}
          {remaining > 0 && (
            <Badge variant="secondary">+{remaining}</Badge>
          )}
        </div>
      )
    },
    size: 350,
  },
  {
    accessorKey: 'id',
    header: withTranslation()(({ column, t }) => <div>{''}</div>),
    cell: withTranslation()(({ row, t }) => {
      const navigate = useNavigate()
      return (
        <div className="w-[100px]">
          <Button
            variant="ghost"
            onClick={() => navigate(`/clients/${row.getValue('id')}`)}
          >
            {t('edit')}
          </Button>
        </div>
      )
    }),
    size: 100,
  },
]
