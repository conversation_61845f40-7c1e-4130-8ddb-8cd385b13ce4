import axios from 'axios';
import CryptoJS from 'crypto-js';

/**
 * Service for reporting invoice data to external endpoints
 */
export default class DataReporter {
  constructor(config) {
    this.config = config || {};
    this.endpoint = process.env.CENTRALIZER_ENDPOINT;
    this.authSalt = process.env.CENTRALIZER_AUTH_SALT;

    if (!this.endpoint) {
      console.warn('No reporting endpoint configured. Data will not be sent to external systems.');
    }
  }

  /**
   * Report all account summaries to the configured endpoint
   * @param {Object} summaries - Object containing conversation and financial summaries
   * @param {string} invoiceId - The invoice ID for reference
   * @returns {Promise<Object>} - Result of the reporting operations
   */
  
  /**
   * Generate authentication headers for the request
   * @param {Object} payload - The request body
   * @returns {Object} - Headers to include with the request
   */
  generateAuthHeaders(payload) {
    const requestBody = JSON.stringify(payload);
    
    try {
      const saltWordArray = CryptoJS.enc.Utf8.parse(this.authSalt);
      const bodyWordArray = CryptoJS.enc.Utf8.parse(requestBody);
      
      const hmac = CryptoJS.HmacSHA256(bodyWordArray, saltWordArray);
      const hashBytes = CryptoJS.enc.Hex.stringify(hmac);
      
      return {
        'User-Agent': 'social',
        'x-social-hash': `sha256=${hashBytes.toLowerCase()}`
      };
    } catch (error) {
      console.error("Error generating HMAC:", error);
      throw error;
    }
  }
  
  async reportAllSummaries(summaries, invoiceId) {
    if (!this.endpoint) {
      console.warn('Skipping data reporting: No endpoint configured');
      return { success: false, reason: 'No endpoint configured' };
    }

    try {
      const metrics = await this.mapToCentralizerMetrics(summaries);
      
      // Create an array of promises for each metrics payload
      const reportPromises = metrics.map(payload => {
        const requestPayload = { ...payload, invoiceId };
        const headers = this.generateAuthHeaders(requestPayload);
        
        return axios.post(this.endpoint, requestPayload, { headers });
      });
      
      // Use Promise.allSettled to handle both successful and failed requests
      const results = await Promise.allSettled(reportPromises);
      
      // Log errors for failed requests
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`Error reporting metrics for client ${metrics[index].client}: ${result.reason}`);
        }
      });

      // Calculate success statistics
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = metrics.length - successCount;

      return {
        success: successCount > 0,
        totalReported: metrics.length,
        successCount,
        failureCount
      };
    } catch (error) {
      console.error('Error reporting summaries:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Send conversation type summaries to the endpoint
   * @param {Array} summaries - List of conversation summaries
   * @param {string} invoiceId - The invoice ID for reference
   * @returns {Promise<Array>} - Results of the POST operations
   */
  async reportConversationSummaries(summaries, invoiceId) {
    console.log(`Reporting ${summaries.length} conversation summaries for invoice ${invoiceId}`);

    const results = [];

    for (const summary of summaries) {
      try {
        const response = await axios.post(`${this.endpoint}/conversation-types`, {
          ...summary,
          invoiceId
        });

        results.push({
          accountNumber: summary.accountNumber,
          success: true,
          status: response.status,
          data: response.data
        });
      } catch (error) {
        console.error(`Error reporting conversation summary for account ${summary.accountNumber}:`, error.message);

        results.push({
          accountNumber: summary.accountNumber,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Send financial summaries to the endpoint
   * @param {Array} summaries - List of financial summaries
   * @param {string} invoiceId - The invoice ID for reference
   * @returns {Promise<Array>} - Results of the POST operations
   */
  async reportFinancialSummaries(summaries, invoiceId) {
    console.log(`Reporting ${summaries.length} financial summaries for invoice ${invoiceId}`);

    const results = [];

    for (const summary of summaries) {
      try {
        const response = await axios.post(`${this.endpoint}/financial-data`, {
          ...summary,
          invoiceId
        });

        results.push({
          accountNumber: summary.accountNumber,
          success: true,
          status: response.status,
          data: response.data
        });
      } catch (error) {
        console.error(`Error reporting financial summary for account ${summary.accountNumber}:`, error.message);

        results.push({
          accountNumber: summary.accountNumber,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  async mapToCentralizerMetrics(summaries) {
    const { conversationSummaries, financialSummaries } = summaries;
    const conversationsPayloads = conversationSummaries.map(conversation => {
      const {
        name: client, typeTotals
      } = conversation;
      const requestPayload = {
        client,
        type: 'Whatsapp',
        counters: typeTotals.map(tt => ({
          name: `conversations_${tt.type.toLowerCase()}`,
          value: tt.conversations
        }))
      }
      return requestPayload;
    });


    const financialsPayloads = financialSummaries.map(financial => {
      const {
        name: client, totalInPdf, yoizen_fee
      } = financial;
      const requestPayload = {
        client,
        type: 'Whatsapp',
        counters: [
          {name: 'financial_usd_totalinpdf', value: totalInPdf},
          {name: 'financial_yoizen_fee', value: yoizen_fee}
        ]
      }
      return requestPayload;
    });

    // Create a map to join payloads by client
    const clientPayloadMap = new Map();
    
    // Process conversation payloads
    conversationsPayloads.forEach(payload => {
      clientPayloadMap.set(payload.client, {
        client: payload.client,
        type: payload.type,
        date: new Date().toISOString(),
        counters: [...payload.counters]
      });
    });
    
    // Process financial payloads, merging with existing entries or creating new ones
    financialsPayloads.forEach(payload => {
      if (clientPayloadMap.has(payload.client)) {
        // Merge counters with existing entry
        const existingPayload = clientPayloadMap.get(payload.client);
        existingPayload.counters = [...existingPayload.counters, ...payload.counters];
      } else {
        // Create new entry
        clientPayloadMap.set(payload.client, {
          client: payload.client,
          type: payload.type,
          date: new Date().toISOString(),
          counters: [...payload.counters]
        });
      }
    });
    
    // Convert map to array
    const combinedPayloads = Array.from(clientPayloadMap.values());

    return combinedPayloads;

  }

}