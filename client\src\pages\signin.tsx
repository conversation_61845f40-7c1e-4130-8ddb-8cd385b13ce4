import { UserAuthForm } from '@/components/auth/user-auth-form'
import { Link } from 'react-router-dom'
import AnimatedLogo from '@/components/ui/animated-logo'
import { useTranslation } from 'react-i18next'

export default function SignIn() {
  const { t } = useTranslation()
  return (
    <>
      <div className="container relative grid min-h-screen flex-col items-center justify-center lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="relative h-full flex-col bg-muted p-10 text-white flex dark:border-r lg:items-center lg:justify-center">
        <div className="absolute inset-0 custom-gradient-background" />

          <div className="relative z-20">
            <AnimatedLogo />
          </div>
        </div>
        <div className="lg:p-8">
          <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
            <div className="flex flex-col space-y-2 text-center">
              <h1 className="text-2xl font-semibold tracking-tight">
                {t('logIn')}
              </h1>
              <p className="text-sm text-muted-foreground">
                {t('logGoogle')}
              </p>
            </div>
            <UserAuthForm />
            <p className="px-8 text-center text-sm text-muted-foreground">
              {t('agreement')}{' '}
              <Link
                to="/terms"
                className="underline underline-offset-4 hover:text-primary"
              >
                {t('termsOfService')}
              </Link>{' '}
              {t('y')}{' '}
              <Link
                to="/privacy"
                className="underline underline-offset-4 hover:text-primary"
              >
                {t('privacyPolice')}
              </Link>
              .
            </p>
          </div>
        </div>
      </div>
    </>
  )
}
